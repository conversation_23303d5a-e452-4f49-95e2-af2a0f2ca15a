import { GeneralApiProblem } from '../api-problem';

// Reset Passcode with OTP Types
export interface ResetPasscodeWithOtpRequest {
  otp: string;
  newPasscode: string;
  confirmPasscode: string;
}

export interface ResetPasscodeWithOtpResponseData {
  status: string;
  message: string;
  success: boolean;
}

export interface ResetPasscodeWithOtpResponse {
  statusCode: null;
  httpStatusCode: number;
  description: null;
  clientMessageId: string;
  timestamp: number;
  data: ResetPasscodeWithOtpResponseData;
}

export type ResetPasscodeWithOtpResult =
  | { kind: "ok" } & ResetPasscodeWithOtpResponse
  | GeneralApiProblem
