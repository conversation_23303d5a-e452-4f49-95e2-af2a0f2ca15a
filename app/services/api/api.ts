/* eslint-disable */
import { ApiResponse, ApisauceInstance, create } from 'apisauce';
import { GeneralApiProblem, getGeneralApiProblem } from './api-problem';
import { ApiConfig, DEFAULT_API_CONFIG } from './api-config';
import uuid from 'uuid-random';
import {
  getToken,
  removeFcmToken,
  removeRefreshToken,
  removeRefreshTokenExpiration,
  removeToken,
  removeTokenExpiration,
} from '@app/services';
import { getRemoveConfig, load, loadString } from '@app/utils/storage';
import _ from 'lodash';
import { removeUtf8 } from '@app/utils/string';
import { EventRegister } from 'react-native-event-listeners';
import { apiServices } from '@app/models/environment';
import { 
  GetNewsListParams, 
  GetNewsCategoriesParams, 
  ListNewsResponse, 
  ListNewsCategoriesResponse, 
  NewsDetailResponse 
} from './interface/news.interface';
import { SalaryAdvanceStatusResponse } from '@app/models/salary/salary-advance.types';
import {
  GenerateOtpRequest,
  GenerateOtpResponse,
  GenerateOtpResult,
  ResendOtpRequest,
  ResendOtpResult,
  VerifyOtpRequest,
  VerifyOtpResponse,
  VerifyOtpResult,
} from '@app/services/api/interface/otp.interface.ts';
import { CheckExistResult } from '@app/services/api/interface/register.interface.ts';
import { FileUploadResponse } from '@app/services/api/interface/file.interface.ts';
import { UpdateProfileRequest } from "./interface/profile.interface"
import {
  ResetPasscodeWithOtpRequest,
  ResetPasscodeWithOtpResult
} from './interface/passcode.interface';

const handlerResponse = (response: any, path: string = 'data.data') => {
  if (!response.ok) {
    const problem = getGeneralApiProblem(response)
    if (problem) return problem
  }
  try {
    return { kind: "ok", ...response.data, data: _.get(response, path) }
  } catch (e: any) {
    __DEV__ && console.log(e.message)
    return { kind: "bad-data", error: e.message, data: {} }
  }
}

/**
 * Manages all requests to the API.
 */
export class Api {
  /**
   * The underlying apisauce instance which performs the requests.
   */
  apisauce!: ApisauceInstance

  /**
   * Configurable options.
   */
  config: ApiConfig

  token!: string

  // Flag to prevent multiple logout processes
  isLoggingOut: boolean = false

  /**
   * Creates the api.
   *
   * @param config The configuration to use.
   */
  constructor(config: ApiConfig = DEFAULT_API_CONFIG) {
    this.config = config //TODO: gán giá trị mặc định
    this.loadConfigFromLocal() // TODO: kéo từ online về
  }

  async loadConfigFromLocal() {
    // TODO: xóa vì đang dùng object
    this.config = await getRemoveConfig()
    __DEV__ && console.log('this.config is already', this.config)
  }

  cleanObject(obj) {
    if (!obj || typeof obj !== 'object') return obj;

    Object.keys(obj).forEach((key) => {
      const value = obj[key];
      if (value === undefined || (typeof value === 'string' && value.trim() === '')) {
        delete obj[key];
      } else if (typeof value === 'object' && value !== null) {
        this.cleanObject(value);
      }
    });

    return obj;
  };

  /**
   * Sets up the API.  This will be called during the bootup
   * sequence and will happen before the first React component
   * is mounted.
   *
   * Be as quick as possible in here.
   */

  async setup(isAuth = true) {
    let headers = {
      Accept: "application/json"
    }

    // construct the apisauce instance
    this.token = await getToken()
    // const location = await load('locationSelected')

    if (this.token !== null && isAuth) {
      headers["access-token"] = this.token
      headers["Authorization"] = `Bearer ${this.token}`
    }

    let location = await load('location')
    if (!_.isEmpty(location)) {
      location = { lat: location.latitude, lng: location.longitude }
      headers["location"] = JSON.stringify(location)
    }

    const provinceSelected = await loadString('provinceSelected')
    if (provinceSelected) {
      headers["province"] = removeUtf8(provinceSelected)
    }

    const districtSelected = await loadString('districtSelected')
    if (districtSelected) {
      headers["district"] = removeUtf8(districtSelected)
    }
    headers["traceId"] = uuid()
    headers["clientMessageId"] = uuid()

    let options: any = {
      baseURL: this.config.url,
      timeout: 60 * 1000,
      headers: headers,
    }
    this.apisauce = create(options)
    this.apisauce.addRequestTransform((request) => {
      request.data = this.cleanObject(request.data); // Làm sạch body
      request.params = this.cleanObject(request.params); // Làm sạch query params (nếu có)
      // Nếu tồn tại biến page trong params thì giảm đi 1
      if (request.params && request.params.page !== undefined && request.params.page > 0) {
        request.params.page = Math.max(0, request.params.page - 1); // Đảm bảo page không nhỏ hơn 0
      }
    });

    // Comment out interceptor and token refresh setup
    this._setupInterceptor()
    // this.setupTokenRefresh();
  }

  // Comment out token refresh methods
  // async _refreshToken() {
  //   try {
  //     const refresh_token = await getRefreshToken();
  //     if (!refresh_token) {
  //       __DEV__ && console.log('No refresh token found');
  //       // Xóa token hiện tại vì không thể làm mới
  //       await removeToken();
  //       return null;
  //     }

  //     __DEV__ && console.log('Attempting to refresh token');

  //     // Gọi API refresh token trực tiếp
  //     const response: ApiResponse<any> = await this.apisauce.post(
  //       "/user/api/v1/user/refresh-token",
  //       { refreshToken: refresh_token }
  //     );

  //     // Check for 401 status code - add logout handler
  //     if (response.status === 401) {
  //       __DEV__ && console.log('Received 401 during token refresh, handling logout');
  //       await this._handleLogout();
  //       return null;
  //     }

  //     if (response.ok && response.data && response.data.data && response.data.data.details) {
  //       const tokenDetails = response.data.data.details;

  //       if (!tokenDetails.access_token || !tokenDetails.refresh_token) {
  //         __DEV__ && console.log('Invalid token response format');
  //         return null;
  //       }

  //       // Lưu token mới và thởi gian hết hạn bằng cách sử dụng tokenService
  //       await saveToken(tokenDetails.access_token);
  //       await saveRefreshToken(tokenDetails.refresh_token);

  //       // Lưu thởi gian hết hạn bằng cách sử dụng tokenService
  //       await saveTokenExpiration(tokenDetails.expires_in);
  //       await saveRefreshTokenExpiration(tokenDetails.refresh_expires_in);

  //       // Cập nhật token trong instance hiện tại
  //       this.token = tokenDetails.access_token;

  //       // Thiết lập timer để refresh token trước khi hết hạn
  //       this._setupRefreshTimer(tokenDetails.expires_in);

  //       __DEV__ && console.log('Token refreshed successfully, expires in:', tokenDetails.expires_in, 'seconds');
  //       return tokenDetails.access_token;
  //     } else {
  //       __DEV__ && console.log('Refresh token failed:', response.problem, response.data);
  //       // Xóa tokens khi refresh thất bại
  //       await removeToken();
  //       await removeTokenExpiration();
  //       return null;
  //     }
  //   } catch (error) {
  //     __DEV__ && console.log('Error refreshing token:', error);
  //     // Xóa tokens khi refresh gặp lỗi
  //     await removeToken();
  //     await removeTokenExpiration();
  //     return null;
  //   }
  // }

  // Timer để refresh token
  // refreshTimer: any = null;

  // // Thiết lập timer để refresh token trước khi hết hạn
  // _setupRefreshTimer(expiresIn: number) {
  //   // Hủy timer cũ nếu có
  //   if (this.refreshTimer) {
  //     clearTimeout(this.refreshTimer);
  //   }

  //   // Tính toán thời gian trước khi token hết hạn
  //   const refreshBefore = Math.min(expiresIn * 0.9, 300); // Refresh trước 10% hoặc 5 phút
  //   const refreshTime = Date.now() + (refreshBefore * 1000);

  //   // Thiết lập timer mới
  //   this.refreshTimer = setTimeout(async () => {
  //     try {
  //       const newToken = await this._refreshToken();
  //       if (newToken) {
  //         // Cập nhật token trong headers
  //         this.apisauce.setHeader('Authorization', `Bearer ${newToken}`);
  //       }
  //     } catch (error) {
  //       __DEV__ && console.log('Error in refresh timer:', error);
  //     }
  //   }, refreshBefore * 1000);
  // }

  async _handleLogout() {
    // Prevent multiple simultaneous logout processes
    if (this.isLoggingOut) {
      __DEV__ && console.log('Logout already in progress, skipping duplicate call');
      return;
    }

    try {
      this.isLoggingOut = true;
      __DEV__ && console.log('Handling logout due to authentication failure');
      // Xóa tất cả token sử dụng tokenService
      await removeToken();
      await removeTokenExpiration();
      await removeRefreshToken();
      await removeRefreshTokenExpiration();
      await removeFcmToken();

      // Phát sự kiện để thông báo session hết hạn
      EventRegister.emit('SESSION_EXPIRED');

      __DEV__ && console.log('Emitted SESSION_EXPIRED event, tokens removed');
    } finally {
      // Reset the flag after a short delay to handle any potential race conditions
      setTimeout(() => {
        this.isLoggingOut = false;
      }, 1000);
    }
  }

  setAuthHeader(token) {
    this.token = token;
    this.apisauce.setHeader('Authorization', `Bearer ${token}`);
    this.apisauce.setHeader('access-token', token);
  }

  async getBanners(): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
      "user/api/get-banner.html?output=json",
      {}
    )
    return handlerResponse(response, 'data.data')
  }

  async getStoreById(storeId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-spa-id.html/${storeId}`,
      {},
    )
    return handlerResponse(response)
  }

  async getCommentStoreById(storeId, page, userId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-comment.html?page=${page}&storeId=${storeId}&output=json`,
      { userId },
    )
    return handlerResponse(response, 'data.data')
  }

  async getCommentProduct(id, page): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/product-rate/${id}?output=json`)
    return handlerResponse(response)
  }

  async getAllCommentOfStore(storeId, page): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-all-comment-of-store.html?page=${page}&storeId=${storeId}&output=json`)
    return handlerResponse(response)
  }

  async postComment(commentModel): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/comment.html",
      commentModel,
    )
    return handlerResponse(response)
  }

  async postCommentProduct(commentCreate): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/product-rate?output=json",
      commentCreate,
    )
    return handlerResponse(response)
  }

  async checkCoupon(code, subTotal, feeShip, storeId): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/calculate-order?output=json",
      { code, subTotal, feeShip, storeId },
    )
    return handlerResponse(response)
  }

  async checkCouponService(code, subTotal, feeShip, storeId): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/calculate-order-service?output=json",
      { code, subTotal, feeShip, storeId },
    )
    return handlerResponse(response)
  }

  async editComment(id, dataCommentEdit): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/edit-comment.html/${id}?output=json`,
      dataCommentEdit,
    )
    return handlerResponse(response)
  }
  async deleteComment(id): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/delete-comment.html/${id}?output=json`,
    )
    return handlerResponse(response)
  }
  async getServiceSpaByStoreId(storeId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-service-spa-by-storeid.html/${storeId}`,
      {},
    )
    return handlerResponse(response)
  }

  async getRoomHotelByStoreId(storeId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-room-hotel-by-storeid.html/${storeId}`,
      {},
    )
    return handlerResponse(response)
  }

  async getServiceOfClinicByStoreId(storeId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-clinic-by-storeid.html/${storeId}`, //TODO đang đợi api để hoàn thiện nốt
      {},
    )
    return handlerResponse(response)
  }

  async getProductSpaByStoreId(storeId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-products-by-storeid.html/${storeId}`,
      {},
    )
    return handlerResponse(response)
  }

  async bookingRoomHotel(storeId, bookingType): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/booking-v2/${storeId}?output=json`,
      { bookingType },
    )
    return handlerResponse(response)
  }

  async postBooking(dataBooking): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/booking-v2?output=json",
      dataBooking,
    )
    return handlerResponse(response)
  }

  async bookingShowroom(dataBooking): Promise<any> {
    // make the api call
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      "user/api/booking-show-room?output=json",
      dataBooking,
    )
    return handlerResponse(response)
  }

  async loginAccount(data): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.post(`${apiServices.services.user}/api/v1/user/login`, data)
    return handlerResponse(response)
  }

  async requestLockAccount(data): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.post(`${apiServices.services.user}/api/v1/user/request-lock-account`, data)
    return handlerResponse(response)
  }

  async registerAccount(data): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(`${apiServices.services.user}/api/v1/user/register`, data)
    return handlerResponse(response)
  }

  async recoverPassword(phoneNumber, password, confirmPassword): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post("user/api/recover-password.html", {
      phone: phoneNumber,
      password: password,
      confirmPassword: confirmPassword

    })
    return handlerResponse(response)
  }

  async changePassword(data): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.put(`${apiServices.services.user}/api/v1/user/change-password`, data)
    return handlerResponse(response)
  }

  async changePasswordResetViaOtp(data): Promise<ApiResponse<VerifyOtpResponse>> {
    await this.setup(false)
    const response: ApiResponse<VerifyOtpResponse> = await this.apisauce.post(`${apiServices.services.user}/api/v1/user/verify-otp-reset-password`, data)
    return response
  }

  async forgotYourPassword(phoneNumber): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post("user/api/forgot-password.html", {
      phone: phoneNumber,
    })
    return handlerResponse(response)
  }

  async getProfileInfo(): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.user}/api/v1/user/profile`)
    return handlerResponse(response)
  }

  async getTransactionLimit(): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(`${apiServices.services.salary}/api/v1/operations/limit`)
    return handlerResponse(response)
  }

  async getTransactionTypes(): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.salary}/api/v1/operations/trans-management`)
    return handlerResponse(response)
  }
  async requestSalaryAdvance(params: any): Promise<{ kind: string; data?: SalaryAdvanceStatusResponse }> {
    await this.setup()
    const response: ApiResponse<SalaryAdvanceStatusResponse> = await this.apisauce.post(  `${apiServices.services.salary}/api/v1/operations/request`, params)
    return handlerResponse(response)
  }
  async verifySalaryAdvance(params): Promise<{ kind: string; data?: SalaryAdvanceStatusResponse }> {
    await this.setup()
    const response: ApiResponse<SalaryAdvanceStatusResponse> = await this.apisauce.post( `${apiServices.services.salary}/api/v1/operations/request-verify`, params)
    return handlerResponse(response)
  }
  async confirmSalaryAdvance(params): Promise<GeneralApiProblem | SalaryAdvanceStatusResponse> {
    await this.setup()
    const response: ApiResponse<SalaryAdvanceStatusResponse> = await this.apisauce.post(`${apiServices.services.salary}/api/v1/operations/confirm`, params)
    return handlerResponse(response)
  }

  /**
   * Check salary advance transaction status by requestId
   * @param params Object containing requestId
   * @returns Transaction status information
   */
  async checkStatusByRequestId(params): Promise<{ kind: string; data?: SalaryAdvanceStatusResponse }> {
    await this.setup()
    const response: ApiResponse<SalaryAdvanceStatusResponse> = await this.apisauce.get(`${apiServices.services.salary}/api/v1/operations/check-status-by-request/${params.requestId}`)
    return handlerResponse(response)
  }
  async getListCompany(textSearch, page = 0, size = 10): Promise<any> {
    await this.setup(true)
    // UI đã bắt đầu từ page 0, trùng với cách API sử dụng
    const params: any = {
      status: 'ACTIVE',
      page: page,
      size: size
    }
    if (textSearch) {
      params['name'] = 'like:' + textSearch
    }
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.portal}/api/v1/partner`, params)
    return handlerResponse(response)
  }
  async getListBankData(): Promise<any> {
    await this.setup(true)
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.salary}/api/v1/operations/bank`)
    return handlerResponse(response)
  }

  async getLichSuGiaoDich(page): Promise<any> {
    await this.setup(true)
    const params: any = {
      page: page,
      size: 10,
    }
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.salary}/api/v1/operations/transaction-history`, params)
    return handlerResponse(response)
  }
  async getNotifications(page): Promise<any> {
    await this.setup(true)
    const params = {
      page: page,
      size: 20,
    }
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.portal}/api/v1/notification/history`, params)
    return handlerResponse(response)
  }
  async getBalance(): Promise<any> {

    await this.setup(true)
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.salary}/api/v1/operations/card`)
    return handlerResponse(response)
  }
  async getLichSuChamCong(params): Promise<any> {
    await this.setup(true)
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.portal}/api/v1/employee/timesheet-history`, params)
    return handlerResponse(response)
  }
  async getBangChamCong(params): Promise<any> {
    await this.setup(true)
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.portal}/api/v1/employee/month-timesheet`, params)
    return handlerResponse(response)
  }
  async getIncome(params): Promise<any> {
    await this.setup(true)
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.portal}/api/v1/employee/income`, params)
    return handlerResponse(response)
  }
  async getListCateGories(): Promise<any> {
    await this.setup(true)
    const params = { page: 0, size: 100 }
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.portal}/api/v1/app-categories`, params)
    return handlerResponse(response)
  }

  async searchSpa(page, search, type, distance, rating, shipping, price, rate, typeShip = 1): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/search-v2?page=${page}&search=${search}&type=${type}&distance=${distance}${rating}&shipping=${shipping}&price=${price}&rate=${rate}&typeShip=${typeShip}`,
    )
    return handlerResponse(response)
  }

  async loadFavorite(page): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/favorites?page=${page}&output=json`,
    )
    return handlerResponse(response)
  }

  async searchServices(type, page, search): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/search-v2?type=${type}&page=${page}&search=${search}&limit=10`,
    )
    return handlerResponse(response)
  }
  async getHistorySearchSpa(user, type): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post("user/api/lich-su-tim-kiem.html?output=json", {
      user,
      type,
    })
    return handlerResponse(response)
  }
  async updateProfile(body: UpdateProfileRequest): Promise<any> {
    await this.setup()
    __DEV__ && console.log(body)
    let dataUpdate = { ...body };
    return await this.apisauce.put(`${apiServices.services.user}/api/v1/user/profile`, dataUpdate)
  }

  async uploadImages(url, files): Promise<FileUploadResponse> {
    // Create the form data object
    const data = new FormData()
    if (files != null && files.length > 0) {
      files.forEach((fileData) => {
        data.append('file', {
          uri: fileData.uri,
          name: fileData.fileName,
          type: fileData.type
        });

      })
    }
    return new Promise(async (resolve) => {
      let headers = {
        Accept: "*/*",
        "content-type": "multipart/form-data",
      }

      let token = await getToken()
      if (token != null) {
        headers["Authorization"] = `Bearer ${token}`
      }
      // Create the config object for the POST
      // You typically have an OAuth2 token that you use for authentication
      const config = {
        method: 'POST',
        headers: headers,
        body: data
      };
      const url = `${this.config.url}${apiServices.services.file}/api/v1/file/upload`
      __DEV__ && console.log('URL Upload IMAGE', url)
      fetch(url, config)
        .then((response) => response.json())
        .then(responseData => {
          return resolve(responseData)
        }).catch(err => {
          return resolve({
            statusCode: null,
            httpStatusCode: 500,
            description: null,
            clientMessageId: uuid(),
            timestamp: Date.now(),
            data: {
              status: 'error',
              message: "Xảy ra lỗi: " + err,
              details: undefined,
              success: false
            }
          });
        })
    })
  }

  async uploadImagesV2(url, files): Promise<any> {
    // Create the form data object
    const urlUpload = `${this.config.url2}${url}`
    console.log('URL Upload IMAGE', urlUpload)
    const data = new FormData()
    if (files != null && files.length > 0) {
      files.forEach((fileData) => {
        data.append('file', {
          uri: fileData.uri,
          name: fileData.fileName,
          type: fileData.type
        });

      })
    }
    return new Promise(async (resolve) => {
      let headers = {
        Accept: "*/*",
        "content-type": "multipart/form-data",
      }

      let token = await getToken()
      if (token != null) {
        headers["access-token"] = token
        headers["Authorization"] = `Bearer ${token}`
      }
      // Create the config object for the POST
      // You typically have an OAuth2 token that you use for authentication
      const config = {
        method: 'POST',
        headers: headers,
        body: data
      };

      try {
        fetch(urlUpload, config)
          .then((response) => response.json())
          .then(responseData => {
            return resolve(responseData)
          }).catch(err => {
            return resolve({ error: true, message: "Xảy ra lỗi: " + err });
          })
      } catch (err) {
        return resolve({ error: true, message: "Xảy ra lỗi upload: " + err });
      }
    })
  }

  async getDataSupportCenter(id): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/ho-tro.html?output=json`, {}
    )
    return handlerResponse(response)
  }
  async getBookingHistory(page, limit = 9999999): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/lay-lich-su-spa.html/1?page=${page}&limit=${limit}&output=json`, {}
    )
    return handlerResponse(response)
  }

  async getBookingHotel(page, limit = 9999999): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/lay-lich-su-khach-san/1?page=${page}&limit=${limit}&output=json`, {}
    )
    return handlerResponse(response)
  }
  async getBookingClinic(page, limit = 9999999): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/lay-lich-su-kham-benh/1?page=${page}&limit=${limit}&output=json`, {}
    )
    return handlerResponse(response)
  }

  async getBookingClassification(page, limit = 9999999): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/get-list-booking-show-room/1?page=${page}&limit=${limit}&output=json`, {}
    )
    return handlerResponse(response)
  }

  async getBookingProduct(page, limit = 9999999): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/lay-lich-su-mua-hang/?page=${page}&limit=${limit}&output=json`, {}
    )
    return handlerResponse(response)
  }

  async getBookingSpaDetail(id): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/order-booking-spa-detail/${id}?output=json`)
    return handlerResponse(response)
  }
  async getBookingHotelDetail(id): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/order-booking-hotel-detail/${id}?output=json`)
    return handlerResponse(response)
  }
  async getBookingClinicDetail(id): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/order-booking-clinic-detail/${id}?output=json`)
    return handlerResponse(response)
  }

  async getBookingProductDetail(orderId): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/order-product-detail/${orderId}?output=json`)
    return handlerResponse(response)
  }

  async getBookingShowRoomDetail(id): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/order-booking-show-room-detail/${id}?output=json`)
    return handlerResponse(response)
  }

  async cancelBooking(id, type, reasonCancel, noteCancel): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/huy-dich-vu.html/${id}/${type}?output=json`, { reasonCancel, noteCancel })
    return handlerResponse(response)
  }

  async cancelBookingProduct(id, reasonCancel, noteCancel): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/huy-don-hang.html/${id}?output=json`, { reasonCancel, noteCancel })
    return handlerResponse(response)
  }

  async getNotification(page, typeNotify = -1): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      "/lay-du-lieu-thong-bao.html?output=json", { page, typeNotify })
    return handlerResponse(response)
  }
  async watchedNotification(id): Promise<any> {
    await this.setup(true)
    const response: ApiResponse<any> = await this.apisauce.put(
      `${apiServices.services.portal}/api/v1/notification/read/${id}`, {})
    return handlerResponse(response)
  }

  async getUnreadNotificationCount(): Promise<any> {
    await this.setup(true)
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.portal}/api/v1/notification/unread/count`, {})
    return handlerResponse(response)
  }
  async getFeatured(storeId: any): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/get-service-spa-featured.html`, { storeId })
    return handlerResponse(response)
  }

  async getServiceFeature(categoryId, storeId): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/get-service-spa-featured.html`, { categoryId, storeId })
    return handlerResponse(response)
  }

  async saveTokenFirebase(body): Promise<any> {
    await this.setup(true)
    const response: ApiResponse<any> = await this.apisauce.post(`portal/api/v1/user/device`, body)
    return handlerResponse(response)
  }

  async getPostsByCategories(categoriesID, page): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `wp/v2/posts?categories=${categoriesID}&per_page=6&_embed&page=${page}`, {})
    return handlerResponse(response)
  }

  async getPosts(catName = ''): Promise<any> {
    let url = `${this.config.url2}/api/list-news?populate=*&sort[0]=id:desc`
    if (catName !== '') {
      url += `&filters[$and][0][categories][name][$contains]=${catName}`
    }
    const response: ApiResponse<any> = await this.apisauce.get(url)
    return handlerResponse(response)
  }
  async getPostDetail(id): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/list-news/${id}?populate=*`)
    return handlerResponse(response)
  }
  async getPostCategories(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/categories-news?pagination%5BwithCount%5D=true`)
    return handlerResponse(response)
  }
  async getCarLine(catName = ''): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/car-categories?pagination%5BwithCount%5D=false&populate=%2A&filters[$and][0][car_brands][name][$contains]=${catName}`)
    return handlerResponse(response)
  }

  async getCarType(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/loai-xes?pagination[withCount]=true&pagination[pageSize]=999&populate=%2A`)
    return handlerResponse(response)
  }
  async getCarTypeByType(type = 'other'): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/car-types?pagination[withCount]=true&pagination[pageSize]=999&populate=%2A&filters[type][$eq]=${type}`)
    return handlerResponse(response)
  }

  async handleCalculate(body): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/booking-hotel-calculator.html?output=json`, body)
    return handlerResponse(response)
  }

  async getPromotion(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-promotion-for-home`)
    return handlerResponse(response)
  }
  async getPromotions(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get("/user/api/get-promotions")
    return handlerResponse(response)
  }
  async getProvinces(): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
      "/user/api/get-list-province")
    return handlerResponse(response)
  }

  async getPromotionById(promotionId): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-promotion-by-id?id=${promotionId}`)
    return handlerResponse(response)
  }

  async getProducts(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/shopping.html`,)
    return handlerResponse(response)
  }

  async getProductDetails(productId): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/chi-tiet-san-pham/${productId}.html?&output=json`,)
    return handlerResponse(response)
  }

  async getProductRate(productId): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/product-rate/${productId}?output=json`,)
    return handlerResponse(response)
  }

  async getShoppingCartInformation(shopProducts): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/tai-thong-tin-gio-hang.html?output=json`, { shopProducts })
    return handlerResponse(response)
  }

  async createOrder(dataBooking): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/create-order-product?output=json`, dataBooking)
    return handlerResponse(response)
  }

  async getCategoryById(categoryId, page, order, shipping, price, rate): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/search-v2?search=&type=0&limit=10&page=${page}&categoryId=${categoryId}&order=${order}&shipping=${shipping}&price=${price}&rate=${rate}`,)
    return handlerResponse(response)
  }

  async getProductForHome(): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/search-v2?page=1&search=&type=0&distance=10&sort=rating&shipping=&price=&rate=&limit=6`,)
    return handlerResponse(response)
  }

  async calculateFeeShip(body): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/calculator-shipping?output=json`, body)
    return handlerResponse(response)
  }

  async updatePetProfile(id, body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/edit-pet?output=json&id=${id}`, body)
    return handlerResponse(response)
  }

  async getListPet(): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-list-car?output=json`,
      {},
    )
    return handlerResponse(response)
  }

  async deletePet(id): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/delete-pet?output=json&id=${id}`,
      {},
    )
    return handlerResponse(response)
  }

  async addPetProfile(body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/add-car?output=json`, body)
    return handlerResponse(response)
  }

  async createDiary(body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `${this.config.url2}/api/car-histories`, body)
    return handlerResponse(response)
  }

  async getDiary(carId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/car-histories?populate=*&filters[car][id][$eq]=${carId}&sort[0]=id:desc`)
    return handlerResponse(response)
  }

  async getPetProfile(petId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/get-pet?output=json&id=${petId}`,
      {},
    )
    return handlerResponse(response)
  }

  async getTopBranch(): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-banner-store.html?tag=store_hot`)
    return handlerResponse(response)
  }

  async getBannerHome2(): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-banner-store.html?tag=banner_home_2`)
    return handlerResponse(response)
  }

  async getShippingInfo(orderId): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-shipping-info/${orderId}`)
    return handlerResponse(response)
  }

  async getNewsForHome(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get("/user/api/get-news")
    return handlerResponse(response)
  }

  async getNewsDetail(id): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(`/user/api/get-news-detail/${id}`)
    return handlerResponse(response)
  }

  async getNewsList(
    page: number = 0,
    size: number = 10,
    categoryId?: number,
    published: boolean = true
  ): Promise<ApiResponse<ListNewsResponse>> {
    await this.setup(false)

    // Build a params object, filter out undefined values
    const params: GetNewsListParams = Object.fromEntries(
      Object.entries({
        page,
        size,
        published,
        categoryId
      }).filter(([_, value]) => value !== undefined)
    ) as GetNewsListParams

    return await this.apisauce.get<ListNewsResponse>(
      `${apiServices.services.portal}/api/v1/news`,
      params
    )
  }

  async getNewsDetailById(id: number): Promise<ApiResponse<NewsDetailResponse>> {
    await this.setup(false)
    return this.apisauce.get<NewsDetailResponse>(
      `${apiServices.services.portal}/api/v1/news/${id}`
    )
  }

  async getNewsCategories(
    page: number = 0,
    size: number = 10
  ): Promise<ApiResponse<ListNewsCategoriesResponse>> {
    await this.setup(false)

    // Build a params object, filter out undefined values
    const params: GetNewsCategoriesParams = Object.fromEntries(
      Object.entries({
        page,
        size
      }).filter(([_, value]) => value !== undefined)
    ) as GetNewsCategoriesParams

    return await this.apisauce.get<ListNewsCategoriesResponse>(
      `${apiServices.services.portal}/api/v1/news-categories`,
      params
    )
  }

  async getBannerShopping(): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-banner-store.html?tag=banner_shopping`)
    return handlerResponse(response)
  }

  async getBannerShoppingTop(): Promise<any> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-banner-store.html?tag=banner_shopping_top`)
    return handlerResponse(response)
  }

  async getProductByCategory(type, page): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-products-for-pet?pet=${type}&page=${page}&limit=10`)
    return handlerResponse(response)
  }

  async getViewedProducts(): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-user-viewed?output=json&field=productIds&page=1&limit=10`)
    return handlerResponse(response)
  }

  async sendProductViewed(send): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/update-user-viewed?output=json`, send)
    return handlerResponse(response)
  }

  async getDistrict(): Promise<any> {
    // await this.setup()
    const provinceSelected = await loadString('provinceSelected')
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-district-by-province?province=${removeUtf8(provinceSelected)}`)
    return handlerResponse(response)
  }

  async loadProductFilter(): Promise<any> {
    // await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/load-product-filter`)
    return handlerResponse(response)
  }

  async getListFanPage(): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/get-banner-store.html?tag=community`)
    return handlerResponse(response)
  }

  async getAlbums(id): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/pet-gallery-by-pet-id/${id}?output=json`)
    return handlerResponse(response)
  }

  async updateAlbums(body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/pet-gallery/update-photo?output=json`, body)
    return handlerResponse(response)
  }
  async getHomeCategories(tag = 'menu'): Promise<any> {
    await this.setup(false)

    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/grid-menu-homes?&populate=*&sort[0]=order&filters[tag][$eq]=${tag}`, {})
    return handlerResponse(response)
  }
  async getCarByUserId(userId: any): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/cars?populate=*&filters[user_id][$eq]=${userId}`, {})
    return handlerResponse(response)
  }

  async addCar(body: any): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `${this.config.url2}/api/cars`, body)
    return handlerResponse(response)
  }
  async editCar(body, id): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.put(
      `${this.config.url2}/api/cars/${id}`, body)
    return handlerResponse(response)
  }

  async getCarById(id): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/cars/${id}?populate=*`)
    return handlerResponse(response)
  }

  async deleteCar(id): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.delete(
      `${this.config.url2}/api/cars/${id}`)
    return handlerResponse(response)
  }

  async deleteImageV2ById(id): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.delete(
      `${this.config.url2}/api/upload/files/${id}`)
    return handlerResponse(response)
  }

  async getClassificationDataByType(storeId, type): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/services-of-brand/${storeId}/${type}`,
      {},
    )
    return handlerResponse(response)
  }

  async getUserPointHistory(): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `user/api/user-point-history?output=json`,
      {},
    )
    return handlerResponse(response)
  }

  async getAppConfig(): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/app-config?populate=*`,
      {},
    )
    return handlerResponse(response)
  }
  async getRewardPointProducts(): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/shops?populate=*&filters[gift_shop_danh_muc][$eq]=2`,
      {},
    )
    return handlerResponse(response)
  }

  async getListItemLuckyWheel(): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/shops?populate=*&filters[gift_shop_danh_muc][$eq]=1`,
      {},
    )
    return handlerResponse(response)
  }

  async buyProductWithPoint(body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/pay-product-with-point?output=json`, body)
    return handlerResponse(response)
  }

  async addFavorite(serviceId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/add-brand-favorite/${serviceId}?output=json`, {})
    return handlerResponse(response)
  }

  async removeFavorite(serviceId): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/remove-brand-favorite/${serviceId}?output=json`, {})
    return handlerResponse(response)
  }

  async pushFcmNotification(toUserId, fromUserId, message): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/push-fcm-notification?output=json`, { toUserId, fromUserId, message })
    return handlerResponse(response)
  }

  async logoutUser(): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(`/user/api/v1/user/logout`, {})
    return handlerResponse(response)
  }

  async getInsurancePosts(catName = ''): Promise<any> {
    let url = `${this.config.url2}/api/bao-hiems?populate=*&sort[0]=id:desc`
    if (catName !== '') {
      url += `&filters[$and][0][loai_bao_hiems][name][$contains]=${catName}`
    }
    const response: ApiResponse<any> = await this.apisauce.get(url)
    return handlerResponse(response)
  }
  async getInsurancePostDetail(id): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/bao-hiems/${id}?populate=*`)
    return handlerResponse(response)
  }
  async getInsuranceCategories(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/loai-bao-hiems?pagination%5BwithCount%5D=true&populate=*`)
    return handlerResponse(response)
  }

  async getCostCategories(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/loai-chi-phis?pagination%5BwithCount%5D=true&populate=*`)
    return handlerResponse(response)
  }

  async getLineChartData(id, m, y): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/report-car-history?id=${id}&m=${m}&y=${y}`)
    return handlerResponse(response)
  }

  async getLoaiXe(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/get-loai-xe-bh`, body)
    return handlerResponse(response)
  }

  async getMaLoaiXe(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/get-ma-loai-xe-bh`, body)
    return handlerResponse(response)
  }

  async tinhPhiTNDSOto(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/tinh-phi-tnds-oto`, body)
    return handlerResponse(response)
  }

  async taoDonOtoV2(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/tao-don-oto`, body)
    return handlerResponse(response)
  }

  async tinhPhiTNDSXMV2(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/tinh-phi-tnds-xm`, body)
    return handlerResponse(response)
  }

  async tinhPhiVCXOtoV2(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/tinh-phi-vcx-oto`, body)
    return handlerResponse(response)
  }

  async taoDonTNDSXMV2(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/tao-don-tnds-xm`, body)
    return handlerResponse(response)
  }

  async createOrderInsuranceHistory(body): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `${this.config.url2}/api/insurance-histories`, body)
    return handlerResponse(response)
  }

  async createUrlPayment(body): Promise<any> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(
      `/user/api/create-url-payment?output=json`, body)
    return handlerResponse(response)
  }

  async getLichSuMuaBHByUserId(userId, bksoat): Promise<any> {
    let filters = `&filters[user_id][$eq]=${userId}&filters[PaymentId][$ne]=CANCEL&filters[active][$eq]=true`
    if (bksoat) {
      filters += `&filters[BienKiemSoat][$eq]=${bksoat}`
    }
    const url = `${this.config.url2}/api/insurance-histories?populate[0]=loai_bao_hiem&populate[1]=loai_bao_hiem.image${filters}&sort[0]=id:desc`
    __DEV__ && console.warn('url', url)
    const response: ApiResponse<any> = await this.apisauce.get(url, {})
    return handlerResponse(response)
  }

  async xoaLichSuMuaBH(id): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.put(
      `${this.config.url2}/api/insurance-histories/${id}`, { data: { active: false } })
    return handlerResponse(response)
  }

  async getUrlPvi(body = {}): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.post(
      `user/api/get-url-bh`, body)
    return handlerResponse(response)
  }

  async getCategoriesByType(type = '0,1,2'): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `/user/api/categories-by-type?output=json&cat=${type}`,)
    return handlerResponse(response)
  }

  async getLuckyWheels(): Promise<any> {
    await this.setup(false)

    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/luck-ky-wheel?populate=*&sort=id:desc`, {})
    return handlerResponse(response)
  }

  async postResultLuckyWheel(body: any): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.post(
      `${this.config.url2}/api/lich-su-quays`, body)
    return handlerResponse(response)
  }

  async checkMaxSpin(user_id: string, idProgram: string): Promise<any> {
    await this.setup()

    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/lich-su-quays/check-max-spin`,
      { user_id, idProgram: idProgram }
    )
    return handlerResponse(response)
  }

  async putUpdateResultLuckyWheel(body: any, id: string): Promise<any> {
    await this.setup()
    // make the api call
    const response: ApiResponse<any> = await this.apisauce.put(
      `${this.config.url2}/api/lich-su-quays/${id}`, body)
    return handlerResponse(response)
  }

  /**
   * get danh sách trúng thưởng
   */
  async getWinners(): Promise<any> {
    const response: ApiResponse<any> = await this.apisauce.get(
      `${this.config.url2}/api/lich-su-quays?populate=*&filters[result][$eq]=true&filters[confirmed][$eq]=true&sort[0]=createdAt:desc`, {})
    return handlerResponse(response)
  }

  _setupInterceptor() {
    this.apisauce.axiosInstance.interceptors.response.use(
      (response) => response, // Trả về nếu phản hồi thành công
      async (error) => {
        const originalRequest = error.config;

        // Kiểm tra xem request hiện tại có phải là request refresh token không
        // để tránh vòng lặp vô hạn khi refresh token cũng trả về 401
        if (originalRequest.url.includes('/user/api/v1/user/refresh-token')) {
          // Nếu refresh token API cũng lỗi, xử lý logout
          await this._handleLogout();
          return Promise.reject(error);
        }

        // Kiểm tra nếu lỗi là 401 và chưa đang trong quá trình logout
        if (error.response?.status === 401 &&
            !originalRequest?.url?.includes('/user/api/v1/user/logout') &&
            !this.isLoggingOut) {
          __DEV__ && console.log('Received 401, handling logout');
          await this._handleLogout();
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Thiết lập mã passcode
   */
  async setupPasscode(data: { oldPasscode?: string; passcode: string; confirmPasscode: string }) {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(`${apiServices.services.portal}/api/v1/passcode/setup`, data)
    return handlerResponse(response)
  }

  /**
   * Xác thực mã passcode
   */
  async verifyPasscode(data: { passcode: string; transactionId?: string }) {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(`${apiServices.services.portal}/api/v1/passcode/verify`, data)
    return handlerResponse(response)
  }

  /**
   * Generate OTP code
   * @param request OTP generation request
   * @returns API response with OTP generation details
   */
  async generateOtp(request: GenerateOtpRequest): Promise<GenerateOtpResult> {
    await this.setup(false)
    const response: ApiResponse<GenerateOtpResponse> = await this.apisauce.post(
      `${apiServices.services.user}/api/v1/otp/generate`,
      request
    )
    return handlerResponse(response)
  }

  async resendOtp(request: ResendOtpRequest): Promise<ResendOtpResult> {
    await this.setup(false)
    const response: ApiResponse<GenerateOtpResponse> = await this.apisauce.post(
      `${apiServices.services.user}/api/v1/otp/resend`,
      request
    )
    return handlerResponse(response)
  }

  /**
   * Verify OTP code
   * @param request OTP verification request
   * @returns API response with verification details
   */
  async verifyOtp(request: VerifyOtpRequest): Promise<VerifyOtpResult> {
    await this.setup(false)  
    const response: ApiResponse<VerifyOtpResponse> = await this.apisauce.post(
      `${apiServices.services.user}/api/v1/otp/verify?updateStatus=${request.type === 'PASSWORD_RESET' ? 'false' : 'true'}`,
      request
    )
    return handlerResponse(response)
  }

  async checkExistUserName(username): Promise<CheckExistResult> {
    await this.setup(false)
    const response: ApiResponse<any> = await this.apisauce.get(
      `${apiServices.services.user}/api/v1/user/check-exist`,
      {
        username,
      }
    )
    return handlerResponse(response)
  }

  /**
   * Reset mã passcode
   */
  async resetPasscode(data: { oldPasscode: string; newPasscode: string; confirmPasscode: string }) {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(`${apiServices.services.portal}/api/v1/passcode/reset`, data)
    return handlerResponse(response)
  }

  /**
   * Reset mã passcode với OTP
   */
  async resetPasscodeWithOtp(data: ResetPasscodeWithOtpRequest): Promise<ResetPasscodeWithOtpResult> {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(`${apiServices.services.portal}/api/v1/passcode/reset-with-otp`, data)
    return handlerResponse(response)
  }

  /**
   * Vô hiệu hóa mã passcode
   */
  async disablePasscode() {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.post(`${apiServices.services.portal}/api/v1/passcode/disable`)
    return handlerResponse(response)
  }

  /**
   * Kiểm tra trạng thái passcode
   */
  async getPasscodeStatus() {
    await this.setup()
    const response: ApiResponse<any> = await this.apisauce.get(`${apiServices.services.portal}/api/v1/passcode/status`)
    return handlerResponse(response)
  }

  /**
   * Gửi yêu cầu tra soát
   * @param transactionId
   * @param reason
   */
  async requestInspection(transactionId: number, reason: string) {
    const response: ApiResponse<any> = await this.apisauce.post(
      `${apiServices.services.salary}/api/v1/operations/request-inspection`,
      {
        transactionId,
        reason,
      }
    )
    return handlerResponse(response)
  }
}
