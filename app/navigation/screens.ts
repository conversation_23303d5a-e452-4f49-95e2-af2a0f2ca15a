export const SCREENS = {
  appLoading: 'APP_LOADING',
  primaryStack: 'PRIMARY_STACK',
  authStack: 'AUTH_STACK',
  settingStack: 'SETTING_STACK',
  luckyWheelStack: 'LUCKY_WHEEL_STACK',
  supportStack: 'SUPPORT_STACK',
  bookingHistoryStack: 'BOOKING_HISTORY_STACK',
  walletStack: 'WALLET_STACK',
  homeStack: 'HOME_STACK',
  totalComment: 'COMMENT_LIST',
  home: 'HOME',
  serviceDetail: 'SERVICE_DETAIL',
  login: 'LOGIN',
  register: 'REGISTER',
  onBoarding: 'ON_BOARDING',
  confirmCode: 'CONFIRM_CODE',
  profileUser: 'PROFILE',
  createPasswordNewAccount: 'CREATE_PASSWORD_NEW_ACCOUNT',
  forgotPassword: 'FORGOT_PASSWORD',
  search: 'SEARCH',
  editProfile: 'EDIT_PROFILE',
  recoverPassword: 'RECOVER_PASSWORD',
  changePasswordScreen: 'CHANGE_PASSWORD_SCREEN',
  generateOtpPasscodeScreen: 'GENERATE_OTP_PASSCODE_SCREEN',
  changePasscodeViaOtpScreen: 'CHANGE_PASSCODE_VIA_OTP_SCREEN',
  supportScreen: 'SUPPORT',
  supportCenterScreen: 'SUPPORT_CENTER',
  bookingHistoryScreen: 'BOOKING_HISTORY',
  notificationScreen: 'NOTIFICATION',
  chatRoomScreen: 'CHATROOM',
  chatDetailScreen: 'CHAT',
  bookingHistoryDetail: 'BOOKING_HISTORY_DETAIL',
  gioiThieu: 'GIOI_THIEU',
  reviewBooking: 'REVIEW_BOOKING',
  payment: 'PAYMENT',
  webview: 'WEBVIEW',
  renderAddress: 'ADDRESS',
  renderAddAddress: 'ADDADDRESS',
  renderEditAddress: 'EDITADDRESS',
  blogStack: 'BLOG_STACK',
  blogScreen: 'BLOG_SCREEN',
  blogDetailScreen: 'BLOG_DETAIL_SCREEN',
  renderChooseService: 'CHOOSE_SERVICE',
  detailStoreScreen: 'DETAIL_STORE_SCREEN',
  shoppingCartScreen: 'SHOPPING_CART_SCREEN',
  promotionDetailScreen: 'PROMOTION_DETAIL_SCREEN',
  shoppingScreen: 'SHOPPING_SCREEN',
  productDetails: 'PRODUCT_DETAILS',
  cartScreen: 'CART_SCREEN',
  reviewBookingProductScreen: 'REVIEW_BOOKING_PRODUCT_SCREEN',
  categoryScreen: 'CATEGORY_SCREEN',
  shoppingStack: 'SHOPPING_STACK',
  carManagement: 'CAR_MANAGEMENT_SCREEN',
  passcodeScreen: 'PASSCODE_SCREEN',
  verifyPasscodeScreen: 'VERIFY_PASSCODE_SCREEN',
  addCar: 'ADD_CAR_SCREEN',
  editCar: 'EDIT_CAR_SCREEN',
  shippingInformationScreen: 'SHIPPING_INFORMATION_SCREEN',
  topBranchScreen: 'TOP_BRANCH_SCREEN',
  newsDetailScreen: 'NEWS_DETAIL_SCREEN',
  allNews: 'ALL_NEWS_SCREEN',
  notificationStack: 'NOTIFICATION_STACK',
  reviewProductScreen: 'REVIEW_PRODUCT_SCREEN',
  communityScreen: 'COMMUNITY_SCREEN',
  petProfileScreen: 'PET_PROFILE_SCREEN',
  addDiaryScreen: 'ADD_DIARY_SCREEN',
  petAlbumsScreen: 'PET_ALBUMS_SCREEN',
  petImageDetail: 'PET_IMAGE_DETAIL',
  rewardPointScreen: 'REWARD_POINT_SCREEN',
  walletScreen: 'WALLET_SCREEN',
  transactionHistoryScreen: 'TRANSACTION_HISTORY_SCREEN',
  transactionHistoryStack: 'TRANSACTION_HISTORY_STACK',
  openCardScreen: 'OPEN_CARD_SCREEN',
  webViewPvComBankScreen: 'WEBVIEW_PV_COMBANK_SCREEN',
  insurance: 'INSURANCE_SC',
  insuranceStack: 'INSURANCE_SCREEN',
  baoHiemTNDSB1: 'BH_TNDS_B1',
  baoHiemTNDSXeMayB1: 'BH_TNDS_XE_MAY_B1',
  baoHiemTNDSXeMayB2: 'BH_TNDS_XE_MAY_B2',
  baoHiemTNDSB2: 'BH_TNDS_B2',
  baoHiemVCXB1: 'BH_VCX_B1',
  baoHiemVCXB2: 'BH_VCX_B2',
  hotServiceScreen: 'hotServiceScreen',
  LuckyWheelScreen: 'LUCKYWHEELSCREEN',
  GiftItemProductScreen: 'GIFTITEMPRODUCTSCREEN',
  ListWinnerScreen: 'LISTWINNERSCREEN',
  luckyWheelRuleScreen: 'LUCKYWHEELRULESCREEN',
  workday: 'WORKDAY',
  workdayTable: 'WORKDAYTABLE',
  transactionVerificationScreen: 'TRANSACTION_VERIFICATION',
  salaryAdvance: 'UNGLUONG',
  confirmAdvanceSalary: 'CONFIRM_ADVANCE_SALARY',
  searchEnterPriseScreen: 'SEARCH_ENTERPRISE_SCREEN',
  changeCompanyScreen: 'CHANGE_COMPANY_SCREEN',
  updateProfileSuccessScreen: 'UPDATE_PROFILE_SUCCESS_SCREEN',
  addBankAccount: 'LIENKETNGANHANG',
  chamCong: 'CHAM_CONG',
  advanceSalarySuccess: 'ADVANCE_SALARY_SUCCESS',
  registerSuccessScreen: 'REGISTER_SUCCESS_SCREEN',
  fastLogin: 'FAST_LOGIN_SCREEN',
  servicesScreen: 'SERVICES_SCREEN',
  yeuCauTraSoatScreen: 'YC_TRA_SOAT_SCREEN',
  tranSactionDetail: 'TRANSACTION_DETAIL_SCREEN'
}