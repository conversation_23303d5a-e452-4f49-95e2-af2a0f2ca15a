/**
 * This is the navigator you will modify to display the logged-in screens of your app.
 * You can use RootNavigator to also display an auth flow or other user flows.
 *
 * You'll likely spend most of your time in this file.
 */
import * as React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import {
  ServiceDetail,
  RenderTotalComment,
  SearchScreen,
  EditProfileScreen,
  ProfileUserScreen,
  RenderAddress,
  RenderAddAddress,
  RenderEditAddress,
  SupportScreen,
  SupportCenterScreen,
  NotificationScreen,
  BookingHistoryScreen,
  ReviewBooking,
  BlogScreen,
  BlogDetail,
  RenderTabDetail,
  ShoppingCartScreen,
  PromotionDetailScreen,
  ShoppingScreen,
  ProductDetailsScreen,
  CartScreen,
  ReviewBookingProductScreen,
  ProductCategoryScreen,
  HomeScreen,
  ChatRoomScreen,
  ChatScreen,
  AddCarScreen,
  ShippingInformationScreen,
  TopBranchScreen,
  NewsDetailScreen,
  AllNewsScreen,
  PaymentScreen,
  BookingHistoryDetailScreen,
  ReviewProductScreen,
  CommunityScreen,
  CarProfileScreen,
  PetAlbumsScreen,
  PetImageDetail,
  CarManagementScreen,
  EditCarScreen,
  WalletScreen,
  TransactionHistory,
  OpenCardScreen, TransactionHistoryScreen, BHTNDSB2Step1Screen, BHVCXOtoScreen, BHTNDSXeMayScreen,
  HotServiceScreen,
  TransactionVerificationScreen,
  ConfirmAdvanceSalaryScreen,
  SearchEnterPriseScreen,
  ChamCongScreen,
  ChangeCompanyScreen,
  UpdateProfileSuccessScreen,
  ServicesScreen,
  YeuCauTraSoatScreen,
  TransactionDetailScreen,
  RecoverPasswordScreen,
  ChangePasswordScreen,
  CreatePasswordNewAccount
} from '../screens'

import { BottomTabBarProps, createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import { TabBar } from '../components/bottom-menu/TabBar'

import { View } from 'react-native'
import { SCREENS } from './screens'
import { Fragment, useContext, useEffect, useLayoutEffect, useState } from 'react';
import { RewardPointScreen } from '@app/screens/reward-point-screen/reward-point-screen'
import { WebviewPvComBankScreen } from '@app/screens/open-bank-card-screen/webview-pvcombank-screen'
import DeviceInfo from 'react-native-device-info'
import { WebviewScreen } from '@app/screens/webview-screen/webview-screen.component'
import LuckyWheelScreen from '@app/components/luck-wheel/lucky-wheel'
import { GiftItemProductScreen } from '@app/components/luck-wheel/gift-item-products'
import { ListWinnerScreen } from '@app/components/luck-wheel/list-winner-screen/list-winner-screen'
import { RuleContentScreen } from '@app/components/luck-wheel/rule-screen/rule-screen'
import { WorkDayScreen } from '@app/screens/workday-history/work-day.screen'
import { SalaryAdvanceScreen } from '@app/screens/salary-advance/salary-advance.screen'
import AddBankAccount from '@app/screens/salary-advance/add-bank-account'
import { AdvanceSalarySuccessScreen } from '@app/screens/advance-salary-success-screen/advance-salary-success-screen'
import { useCurrentStackIndex } from '@app/use-hooks';
import { getFocusedRouteNameFromRoute } from '@react-navigation/native';
import { ModalContext } from '@app/components/modal-success';
import { useStores } from '@app/models';
import { EVENT } from "@app/constants/event";
import { PasscodeScreen } from '@app/screens/passcode-screen/passcode-screen'
import { VerifyPasscodeScreen } from '@app/screens/passcode-screen/verify-passcode-screen'
import { HIDE_TAB_BAR, SHOW_TAB_BAR } from '@app/constants/configs.ts';
// TAB HOME
const HomeStack = createNativeStackNavigator()
export function HomeStackScreen({ navigation, route }) {
  useLayoutEffect(() => {
    const currentStackIndex = useCurrentStackIndex(navigation);
    console.log("🚀 ~ useLayoutEffect ~ currentStackIndex:", currentStackIndex)
    navigation.setOptions({ tabBarVisible: currentStackIndex < 1 });
  }, [navigation, route])

  return (
    <HomeStack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true
      }}
      initialRouteName={SCREENS.home}>
      <HomeStack.Screen name={SCREENS.home} component={HomeScreen} />
      <HomeStack.Screen name={SCREENS.search} component={SearchScreen} />
      <HomeStack.Screen name={SCREENS.serviceDetail} component={ServiceDetail} />
      <HomeStack.Screen name={SCREENS.totalComment} component={RenderTotalComment} />
      <HomeStack.Screen name={SCREENS.reviewBooking} component={ReviewBooking} />
      <HomeStack.Screen name={SCREENS.payment} component={PaymentScreen} />
      <HomeStack.Screen name={SCREENS.webview} component={WebviewScreen} />
      <HomeStack.Screen name={SCREENS.renderAddAddress} component={RenderAddAddress} />
      <HomeStack.Screen name={SCREENS.detailStoreScreen} component={RenderTabDetail} />
      <HomeStack.Screen name={SCREENS.shoppingCartScreen} component={ShoppingCartScreen} />
      <HomeStack.Screen name={SCREENS.promotionDetailScreen} component={PromotionDetailScreen} />
      <HomeStack.Screen name={SCREENS.productDetails} component={ProductDetailsScreen} />
      <HomeStack.Screen name={SCREENS.cartScreen} component={CartScreen} />
      <HomeStack.Screen name={SCREENS.reviewBookingProductScreen} component={ReviewBookingProductScreen} />
      <HomeStack.Screen name={SCREENS.categoryScreen} component={ProductCategoryScreen} />
      <HomeStack.Screen name={SCREENS.shoppingScreen} component={ShoppingScreen} />
      <HomeStack.Screen name={SCREENS.blogScreen} component={BlogScreen} />
      <HomeStack.Screen name={SCREENS.blogDetailScreen} component={BlogDetail} />
      <HomeStack.Screen name={SCREENS.bookingHistoryScreen} component={BookingHistoryScreen} />
      <HomeStack.Screen name={SCREENS.bookingHistoryDetail} component={BookingHistoryDetailScreen} />
      <HomeStack.Screen name={SCREENS.topBranchScreen} component={TopBranchScreen} />
      <HomeStack.Screen name={SCREENS.newsDetailScreen} component={NewsDetailScreen} />
      <HomeStack.Screen name={SCREENS.allNews} component={AllNewsScreen} />
      <HomeStack.Screen name={SCREENS.chatDetailScreen} component={ChatScreen} />
      <HomeStack.Screen name={SCREENS.chatRoomScreen} component={ChatRoomScreen} />
      <HomeStack.Screen name={SCREENS.notificationScreen} component={NotificationScreen} />
      <HomeStack.Screen name={SCREENS.reviewProductScreen} component={ReviewProductScreen} />
      <HomeStack.Screen name={SCREENS.communityScreen} component={CommunityScreen} />
      <HomeStack.Screen name={SCREENS.supportScreen} component={SupportScreen} />
      <HomeStack.Screen name={SCREENS.petProfileScreen} component={CarProfileScreen} />
      <HomeStack.Screen name={SCREENS.addCar} component={AddCarScreen} />
      <HomeStack.Screen name={SCREENS.petAlbumsScreen} component={PetAlbumsScreen} />
      <HomeStack.Screen name={SCREENS.petImageDetail} component={PetImageDetail} />
      <HomeStack.Screen name={SCREENS.editCar} component={EditCarScreen} />
      <HomeStack.Screen name={SCREENS.supportCenterScreen} component={SupportCenterScreen} />
      <HomeStack.Screen name={SCREENS.blogStack} component={BlogStackScreen} />
      <HomeStack.Screen name={SCREENS.hotServiceScreen} component={HotServiceScreen} />
      <HomeStack.Screen name={SCREENS.insuranceStack} component={InsuranceStackScreen} />
      <HomeStack.Screen name={SCREENS.baoHiemTNDSB1} component={BHTNDSB2Step1Screen} />
      <HomeStack.Screen name={SCREENS.baoHiemVCXB1} component={BHVCXOtoScreen} />
      <HomeStack.Screen name={SCREENS.baoHiemTNDSXeMayB1} component={BHTNDSXeMayScreen} />
      <HomeStack.Screen name={SCREENS.openCardScreen} component={OpenCardScreen} />
      <HomeStack.Screen name={SCREENS.luckyWheelStack} component={LuckyWheelStackScreen} />
      <HomeStack.Screen name={SCREENS.workday} component={WorkDayScreen} />
      <HomeStack.Screen name={SCREENS.transactionVerificationScreen} component={TransactionVerificationScreen} />
      <HomeStack.Screen name={SCREENS.salaryAdvance} component={SalaryAdvanceScreen} />
      <HomeStack.Screen name={SCREENS.addBankAccount} component={AddBankAccount} />
      <HomeStack.Screen name={SCREENS.confirmAdvanceSalary} component={ConfirmAdvanceSalaryScreen} />
      <HomeStack.Screen name={SCREENS.searchEnterPriseScreen} component={SearchEnterPriseScreen} />
      <HomeStack.Screen name={SCREENS.chamCong} component={ChamCongScreen} />
      <HomeStack.Screen name={SCREENS.advanceSalarySuccess} component={AdvanceSalarySuccessScreen} />
      <HomeStack.Screen name={SCREENS.servicesScreen} component={ServicesScreen} />
      <HomeStack.Screen name={SCREENS.yeuCauTraSoatScreen} component={YeuCauTraSoatScreen} />
      <HomeStack.Screen name={SCREENS.tranSactionDetail} component={TransactionDetailScreen} />
    </HomeStack.Navigator>
  )
}

// TAB SETTING
const SettingsStack = createNativeStackNavigator()
function SettingsStackScreen({ navigation, route }) {
  // const defaultScreen = props.route?.params?.screen || SCREENS.profileUser
  useLayoutEffect(() => {
    const currentStackIndex = useCurrentStackIndex(navigation);
    navigation.setOptions({ tabBarVisible: currentStackIndex < 1 });
  }, [navigation, route])

  return (
    <SettingsStack.Navigator
      // initialRouteName={defaultScreen}
      screenOptions={{
        headerShown: false,
        gestureEnabled: true
      }}>
      <SettingsStack.Screen name={SCREENS.profileUser} component={ProfileUserScreen} />
      <SettingsStack.Screen name={SCREENS.editProfile} component={EditProfileScreen} />
      <SettingsStack.Screen name={SCREENS.renderAddress} component={RenderAddress} />
      <SettingsStack.Screen name={SCREENS.renderAddAddress} component={RenderAddAddress} />
      <SettingsStack.Screen name={SCREENS.renderEditAddress} component={RenderEditAddress} />
      <SettingsStack.Screen name={SCREENS.bookingHistoryScreen} component={BookingHistoryScreen} />
      <SettingsStack.Screen name={SCREENS.bookingHistoryStack} component={BookingHistoryStackScreen} />
      <SettingsStack.Screen name={SCREENS.supportStack} component={SupportStackScreen} />
      <SettingsStack.Screen name={SCREENS.notificationScreen} component={NotificationScreen} />
      <SettingsStack.Screen name={SCREENS.chatRoomScreen} component={ChatRoomScreen} />
      <SettingsStack.Screen name={SCREENS.chatDetailScreen} component={ChatScreen} />
      <SettingsStack.Screen name={SCREENS.bookingHistoryDetail} component={BookingHistoryDetailScreen} />
      <SettingsStack.Screen name={SCREENS.carManagement} component={CarManagementScreen} />
      <SettingsStack.Screen name={SCREENS.addCar} component={AddCarScreen} />
      <SettingsStack.Screen name={SCREENS.editCar} component={EditCarScreen} />
      <SettingsStack.Screen name={SCREENS.walletScreen} component={WalletScreen} />
      <SettingsStack.Screen name={SCREENS.rewardPointScreen} component={RewardPointScreen} />
      <SettingsStack.Screen name={SCREENS.transactionHistoryScreen} component={TransactionHistory} />
      <SettingsStack.Screen name={SCREENS.openCardScreen} component={OpenCardScreen} />
      <SettingsStack.Screen name={SCREENS.webViewPvComBankScreen} component={WebviewPvComBankScreen} />
      <SettingsStack.Screen name={SCREENS.blogDetailScreen} component={BlogDetail} />
      <SettingsStack.Screen name={SCREENS.luckyWheelStack} component={LuckyWheelStackScreen} />
      <SettingsStack.Screen name={SCREENS.transactionVerificationScreen} component={TransactionVerificationScreen} />
      <SettingsStack.Screen name={SCREENS.chamCong} component={ChamCongScreen} />
      <SettingsStack.Screen name={SCREENS.searchEnterPriseScreen} component={SearchEnterPriseScreen} />
      <SettingsStack.Screen name={SCREENS.changeCompanyScreen} component={ChangeCompanyScreen} />
      <SettingsStack.Screen name={SCREENS.updateProfileSuccessScreen} component={UpdateProfileSuccessScreen} />
      <SettingsStack.Screen name={SCREENS.passcodeScreen} component={PasscodeScreen} />
      <SettingsStack.Screen name={SCREENS.verifyPasscodeScreen} component={VerifyPasscodeScreen} />
      <SettingsStack.Screen name={SCREENS.recoverPassword} component={RecoverPasswordScreen} />
      <SettingsStack.Screen name={SCREENS.changePasswordScreen} component={ChangePasswordScreen} />
      <SettingsStack.Screen name={SCREENS.changePasscodeViaOtpScreen} component={ChangePasscodeViaOtpScreen} />
      <SettingsStack.Screen name={SCREENS.createPasswordNewAccount} component={CreatePasswordNewAccount} />
    </SettingsStack.Navigator>
  )
}

// TAB SETTING
const WalletStack = createNativeStackNavigator()
function WalletStackStackScreen({ navigation, route }) {
  // const defaultScreen = props.route?.params?.screen || SCREENS.profileUser
  useLayoutEffect(() => {
    const currentStackIndex = useCurrentStackIndex(navigation);
    navigation.setOptions({ tabBarVisible: currentStackIndex < 1 });
  }, [navigation, route])

  return (
    <WalletStack.Navigator
      initialRouteName={SCREENS.walletScreen}
      screenOptions={{
        headerShown: false,
        gestureEnabled: true
      }}>
      <WalletStack.Screen name={SCREENS.walletScreen} component={WalletScreen} />
      <WalletStack.Screen name={SCREENS.rewardPointScreen} component={RewardPointScreen} />
      <WalletStack.Screen name={SCREENS.transactionHistoryScreen} component={TransactionHistory} />
      <WalletStack.Screen name={SCREENS.openCardScreen} component={OpenCardScreen} />
      <WalletStack.Screen name={SCREENS.webViewPvComBankScreen} component={WebviewPvComBankScreen} />
      <WalletStack.Screen name={SCREENS.blogDetailScreen} component={BlogDetail} />
      <WalletStack.Screen name={SCREENS.luckyWheelStack} component={LuckyWheelStackScreen} />
    </WalletStack.Navigator>
  )
}

// TAB SHOPPING
const ShoppingStack = createNativeStackNavigator()
function ShoppingStackScreen({ navigation, route }) {
  // useLayoutEffect(() => {
  //   // const routeName = getFocusedRouteNameFromRoute(route)
  //   if (route.state && route.state.index > 0) {
  //     //navigation.setOptions({ tabBarVisible: false })
  //   } else {
  //     //navigation.setOptions({ tabBarVisible: true })
  //   }
  // }, [navigation, route])

  // Lấy index của stack hiện tại
  // const currentStackIndex = useNavigation<any>()State((state) => {
  //   const currentRoute = state.routes[state.index];
  //   return currentRoute.state?.index || 0; // Mặc định là 0 nếu chưa có state
  // });
  //
  // useEffect(() => {
  //   // Ẩn/hiện tab bar dựa trên index
  //   // setIsTabBarVisible(currentStackIndex === 0);
  //   navigation.setOptions({ tabBarVisible: currentStackIndex === 0 })
  // }, [currentStackIndex]);

  return (
    <ShoppingStack.Navigator
      // initialRouteName={defaultScreen}
      screenOptions={{
        headerShown: false,
        gestureEnabled: true
      }}>
      <ShoppingStack.Screen name={SCREENS.shoppingScreen} component={ShoppingScreen} />
      <ShoppingStack.Screen name={SCREENS.productDetails} component={ProductDetailsScreen} />
      <ShoppingStack.Screen name={SCREENS.cartScreen} component={CartScreen} />
      <ShoppingStack.Screen name={SCREENS.reviewBookingProductScreen} component={ReviewBookingProductScreen} />
      <ShoppingStack.Screen name={SCREENS.categoryScreen} component={ProductCategoryScreen} />
      <ShoppingStack.Screen name={SCREENS.payment} component={PaymentScreen} />
      <ShoppingStack.Screen name={SCREENS.bookingHistoryDetail} component={BookingHistoryDetailScreen} />
      <ShoppingStack.Screen name={SCREENS.bookingHistoryScreen} component={BookingHistoryScreen} />
      <ShoppingStack.Screen name={SCREENS.chatDetailScreen} component={ChatScreen} />
      <ShoppingStack.Screen name={SCREENS.chatRoomScreen} component={ChatRoomScreen} />
      <ShoppingStack.Screen name={SCREENS.promotionDetailScreen} component={PromotionDetailScreen} />
      <ShoppingStack.Screen name={SCREENS.search} component={SearchScreen} />
      <ShoppingStack.Screen name={SCREENS.renderAddress} component={RenderAddress} />
      <ShoppingStack.Screen name={SCREENS.renderAddAddress} component={RenderAddAddress} />
      <ShoppingStack.Screen name={SCREENS.shippingInformationScreen} component={ShippingInformationScreen} />
      <ShoppingStack.Screen name={SCREENS.reviewProductScreen} component={ReviewProductScreen} />
      <ShoppingStack.Screen name={SCREENS.notificationScreen} component={NotificationScreen} />
      <ShoppingStack.Screen name={SCREENS.serviceDetail} component={ServiceDetail} />
      <ShoppingStack.Screen name={SCREENS.totalComment} component={RenderTotalComment} />
      <ShoppingStack.Screen name={SCREENS.shoppingCartScreen} component={ShoppingCartScreen} />
      <ShoppingStack.Screen name={SCREENS.reviewBooking} component={ReviewBooking} />
      <ShoppingStack.Screen name={SCREENS.detailStoreScreen} component={RenderTabDetail} />
      <ShoppingStack.Screen name={SCREENS.supportScreen} component={SupportScreen} />
      <ShoppingStack.Screen name={SCREENS.supportCenterScreen} component={SupportCenterScreen} />
      <ShoppingStack.Screen name={SCREENS.luckyWheelStack} component={LuckyWheelStackScreen} />
    </ShoppingStack.Navigator>
  )
}
//
const BlogStack = createNativeStackNavigator()
// TAB SETTING
function BlogStackScreen({ navigation, route }) {
  useLayoutEffect(() => {
    const currentStackIndex = useCurrentStackIndex(navigation);
    navigation.setOptions({ tabBarVisible: currentStackIndex < 1 });
  }, [navigation, route])
  return (
    <BlogStack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true
      }}>
      <BlogStack.Screen name={SCREENS.blogScreen} component={BlogScreen} />
      <BlogStack.Screen name={SCREENS.blogDetailScreen} component={BlogDetail} />
    </BlogStack.Navigator>
  )
}

// TAB SUPPORT
const SupportStack = createNativeStackNavigator()
function SupportStackScreen() {
  return (
    <SupportStack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true
      }}>
      <SupportStack.Screen name={SCREENS.supportScreen} component={SupportScreen} />
      <SupportStack.Screen name={SCREENS.supportCenterScreen} component={SupportCenterScreen} />
      <SupportStack.Screen name={SCREENS.newsDetailScreen} component={NewsDetailScreen} />
    </SupportStack.Navigator>
  )
}

// TAB BOOKING HISTORY
const BookingHistoryStack = createNativeStackNavigator()

function BookingHistoryStackScreen({ navigation, route }) {
  useLayoutEffect(() => {
    const currentStackIndex = useCurrentStackIndex(navigation);
    navigation.setOptions({ tabBarVisible: currentStackIndex < 1 });
  }, [navigation, route])
  return (
    <BookingHistoryStack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true
      }}>
      <BookingHistoryStack.Screen name={SCREENS.bookingHistoryScreen} component={BookingHistoryScreen} />
      <BookingHistoryStack.Screen name={SCREENS.bookingHistoryDetail} component={BookingHistoryDetailScreen} />
      <BookingHistoryStack.Screen name={SCREENS.shippingInformationScreen} component={ShippingInformationScreen} />
    </BookingHistoryStack.Navigator>
  )
}

// TAB NOTIFICATION
// const NotificationStack = createNativeStackNavigator()
//
// function NotificationStackScreen({ navigation, route }) {
//   useLayoutEffect(() => {
//     if (route.state && route.state.index > 0) {
//       navigation.setOptions({ tabBarVisible: false })
//     } else {
//       navigation.setOptions({ tabBarVisible: true })
//     }
//   }, [navigation, route])
//   return (
//     <NotificationStack.Navigator
//       screenOptions={{
//         headerShown: false,
//         gestureEnabled: true
//       }}>
//       <NotificationStack.Screen name={SCREENS.notificationScreen} component={NotificationScreen} />
//       <NotificationStack.Screen name={SCREENS.bookingHistoryScreen} component={InsuranceScreen} />
//       <NotificationStack.Screen name={SCREENS.bookingHistoryDetail} component={BookingHistoryDetailScreen} />
//     </NotificationStack.Navigator>
//   )
// }

const InsuranceStack = createNativeStackNavigator()
function InsuranceStackScreen({ navigation, route }) {
  useLayoutEffect(() => {
    const currentStackIndex = useCurrentStackIndex(navigation);
    navigation.setOptions({ tabBarVisible: currentStackIndex < 1 });
  }, [navigation, route])
  return (
    <InsuranceStack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true
      }}
      initialRouteName={SCREENS.insurance}>
      <InsuranceStack.Screen name={SCREENS.insurance} component={TransactionHistoryScreen} />
      <InsuranceStack.Screen name={SCREENS.baoHiemTNDSB1} component={BHTNDSB2Step1Screen} />
      <InsuranceStack.Screen name={SCREENS.baoHiemVCXB1} component={BHVCXOtoScreen} />
      <InsuranceStack.Screen name={SCREENS.baoHiemTNDSXeMayB1} component={BHTNDSXeMayScreen} />
      <InsuranceStack.Screen name={SCREENS.payment} component={PaymentScreen} />
      <InsuranceStack.Screen name={SCREENS.luckyWheelStack} component={LuckyWheelStackScreen} />
    </InsuranceStack.Navigator>
  )
}

const LuckyWheelStack = createNativeStackNavigator()

function LuckyWheelStackScreen({ navigation, route }) {
  useLayoutEffect(() => {
    const currentStackIndex = useCurrentStackIndex(navigation);
    navigation.setOptions({ tabBarVisible: currentStackIndex < 1 });
  }, [navigation, route])

  return (
    <LuckyWheelStack.Navigator
      initialRouteName={SCREENS.LuckyWheelScreen}
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
      }}
    >
      <LuckyWheelStack.Screen name={SCREENS.LuckyWheelScreen} component={LuckyWheelScreen} />
      <LuckyWheelStack.Screen name={SCREENS.GiftItemProductScreen} component={GiftItemProductScreen} />
      <LuckyWheelStack.Screen name={SCREENS.ListWinnerScreen} component={ListWinnerScreen} />
      <LuckyWheelStack.Screen name={SCREENS.luckyWheelRuleScreen} component={RuleContentScreen} />
      {/* Add more screens as needed */}
    </LuckyWheelStack.Navigator>
  )
}

const TransactionHistoryStack = createNativeStackNavigator()

function TransactionHistoryStackScreen({ navigation, route }) {
  useLayoutEffect(() => {
    const routeName = getFocusedRouteNameFromRoute(route)
    // Ẩn tab bar khi đang ở màn hình con trong stack
    navigation.setOptions({
      tabBarVisible: !routeName || routeName === SCREENS.insurance
    })
  }, [navigation, route])

  return (
    <TransactionHistoryStack.Navigator
      initialRouteName={SCREENS.insurance}
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
      }}
    >
      <TransactionHistoryStack.Screen name={SCREENS.insurance} component={TransactionHistoryScreen} />
      <TransactionHistoryStack.Screen name={SCREENS.yeuCauTraSoatScreen} component={YeuCauTraSoatScreen} />
      <TransactionHistoryStack.Screen name={SCREENS.tranSactionDetail} component={TransactionDetailScreen} />
    </TransactionHistoryStack.Navigator>
  )
}

const Tab = createBottomTabNavigator()
// TODO: cấu hình icon tabbar trong file BottomMenuItem.tsx
export const TabNavigator = ({ navigation, route }) => {
  const { showCustomError, showError, showConfirm, hideAppLoading } = useContext(ModalContext)
  const [lastCheckedTime, setLastCheckedTime] = useState(null)
  const { profileStore, pubSubStore } = useStores()
  const [isTabBarVisible, setIsTabBarVisible] = useState(true);
  // Lắng nghe sự kiện navigation từ pubsub để giải quyết vấn đề UI đóng băng
  useEffect(() => {
    if (!pubSubStore) return
    
    const handleNavigation = (data: { screen: string; params?: any; stack?: string }) => {
      __DEV__ && console.log('📱 PUBSUB NAVIGATION - Event received:', JSON.stringify(data, null, 2))
      if (!data) {
        __DEV__ && console.log('📱 PUBSUB NAVIGATION - Empty data received, ignoring')
        return
      }
      
      // Sử dụng setTimeout để đảm bảo việc navigate diễn ra sau khi modal đóng hoàn toàn
      __DEV__ && console.log('📱 PUBSUB NAVIGATION - Scheduling navigation with 100ms delay')
      setTimeout(() => {
        try {
          const { screen, params, stack } = data
          __DEV__ && console.log('📱 PUBSUB NAVIGATION - Delay complete, executing navigation')
          
          if (stack) {
            // Navigate đến stack trước, sau đó đến màn hình cụ thể
            __DEV__ && console.log(`📱 PUBSUB NAVIGATION - Navigating to stack: ${stack}, screen: ${screen}`)
            __DEV__ && console.log('📱 PUBSUB NAVIGATION - With params:', JSON.stringify(params, null, 2))
            navigation.navigate(stack, { screen, params })
          } else {
            // Navigate trực tiếp đến màn hình
            __DEV__ && console.log(`📱 PUBSUB NAVIGATION - Navigating directly to screen: ${screen}`)
            __DEV__ && console.log('📱 PUBSUB NAVIGATION - With params:', JSON.stringify(params, null, 2))
            navigation.navigate(screen, params)
          }
          
          // Ẩn loading nếu còn hiển thị
          hideAppLoading()
          __DEV__ && console.log(`📱 PUBSUB NAVIGATION - Navigation complete: ${stack ? stack + ' > ' : ''}${screen}`)
        } catch (error) {
          console.error('📱 PUBSUB NAVIGATION - ERROR:', error)
        }
      }, 100) // Delay nhỏ để đảm bảo UI không đóng băng
    }
    
    // Đăng ký lắng nghe sự kiện navigation
    pubSubStore.subscribe(EVENT.NAVIGATION, handleNavigation)
    
    return () => {
      // Hủy đăng ký khi component unmount
      pubSubStore.unsubscribe(EVENT.NAVIGATION, handleNavigation)
    }
  }, [pubSubStore, navigation, hideAppLoading])
  
  useLayoutEffect(() => {
    const routeName = getFocusedRouteNameFromRoute(route)
    __DEV__ && console.log('routeNameRoot', routeName)
    hideAppLoading()
    if (routeName === SCREENS.home) {
      // Use conditionally to avoid errors if pubSubStore is undefined
      pubSubStore?.publish?.(SHOW_TAB_BAR, null)
    }
    // if (routeName !== SCREENS.guideScreen && profileStore.isSignedIn()) {
    //   const currentTime = moment()
    //
    //   // Kiểm tra nếu thời gian chưa quá 5 phút từ lần bấm trước đó
    //   if (lastCheckedTime && currentTime.diff(lastCheckedTime, 'minutes') < 1) {
    //     return // Nếu đã bấm trong 5 phút qua thì không gọi API nữa
    //   }
    //   // gọi api check quyền
    //   profileStore.getProfile().then(r => {
    //     try {
    //       const expireAt = moment(r?.data?.expireAt)
    //       const now = moment()
    //       const onClosed = () => {
    //         navigation.navigate(SCREENS.guideScreen)
    //       }
    //       if (expireAt.isBefore(now) || !r?.data?.expireAt) {
    //         showConfirm('Thông báo', 'Tài khoản của bạn đã hết hạn, để tiếp tục sử dụng dịch vụ vui lòng gia hạn', 'Để sau','Gia hạn ngay', () => {
    //           setLastCheckedTime(currentTime) // Lưu thời gian khi người dùng bấm
    //         }, () => {
    //           // @ts-ignore
    //           setLastCheckedTime(currentTime) // Lưu thời gian khi người dùng bấm
    //           onClosed()
    //         })
    //       }
    //     } catch (e) {
    //
    //     }
    //   })
    // }
  }, [navigation, route, profileStore, lastCheckedTime])

  useEffect(() => {
    // Đăng ký sự kiện show/hide TabBar
    const showHandler = () => {
      __DEV__ && console.log('SHOW_TAB_BAR');
      setIsTabBarVisible(true);
    };
    const hideHandler = () => {
      __DEV__ && console.log('HIDE_TAB_BAR');
      setIsTabBarVisible(false);
    };

    pubSubStore.subscribe(SHOW_TAB_BAR, showHandler);
    pubSubStore.subscribe(HIDE_TAB_BAR, hideHandler);

    // Dọn dẹp sự kiện khi unmount
    return () => {
      __DEV__ && console.log('Unsubscribe SHOW_TAB_BAR and HIDE_TAB_BAR');
      pubSubStore.unsubscribe(SHOW_TAB_BAR, showHandler);
      pubSubStore.unsubscribe(HIDE_TAB_BAR, hideHandler);
    };
  }, [pubSubStore]);

  return (
    // eslint-disable-next-line react-native/no-inline-styles
    <Fragment>
      {/* { isAndroid ? <View style={styles.AndroidSafeArea} /> : null} */}
      <View style={[{ flex: 1, position: 'relative' }, { marginTop: DeviceInfo.hasDynamicIsland() ? 0 : 0 }]}>
        <Tab.Navigator
          initialRouteName={SCREENS.homeStack}
          screenOptions={{
            headerShown: false,
          }}
          tabBar={(props: BottomTabBarProps) => isTabBarVisible ? <TabBar {...props} /> : null}
        >
          <Tab.Screen name={SCREENS.homeStack} options={{ tabBarLabel: 'Trang chủ' }} component={HomeStackScreen} />
          {/* <Tab.Screen name={SCREENS.homeStack} options={{ tabBarLabel: 'Trang chủ' }} component={ShoppingCartScreen} /> */}
          {/* <Tab.Screen name={SCREENS.blogStack} options={{ tabBarLabel: 'Tin tức' }} component={BlogStackScreen} /> */}
          <Tab.Screen name={SCREENS.transactionHistoryStack} options={{ tabBarLabel: 'Lịch sử GD' }} component={TransactionHistoryStackScreen} />
          {/* <Tab.Screen name={SCREENS.walletStack} options={{ tabBarLabel: 'Ví' }} component={WalletStackStackScreen} /> */}
          <Tab.Screen name={SCREENS.workday} options={{ tabBarLabel: 'Chấm công' }} component={WorkDayScreen} />
          <Tab.Screen name={SCREENS.settingStack} options={{ tabBarLabel: 'Tài khoản' }} component={SettingsStackScreen} />
        </Tab.Navigator>
        {/*{useSafeAreaInsets().bottom > 0 && (*/}
        {/* <View*/}
        {/*   style={{*/}
        {/*     height: useSafeAreaInsets().bottom,*/}
        {/*     backgroundColor: 'white'*/}
        {/*   }}*/}
        {/* />*/}
        {/*)}*/}
      </View>

    </Fragment>
  )
}

// TAB SETTING
const SalaryAdvanceStack = createNativeStackNavigator()
function SalaryAdvanceStackScreen({ navigation, route }) {
  // const defaultScreen = props.route?.params?.screen || SCREENS.profileUser
  useLayoutEffect(() => {
    const currentStackIndex = useCurrentStackIndex(navigation);
    navigation.setOptions({ tabBarVisible: currentStackIndex < 1 });
  }, [navigation, route])

  return (
    <SalaryAdvanceStack.Navigator
      initialRouteName={SCREENS.walletScreen}
      screenOptions={{
        headerShown: false,
        gestureEnabled: true
      }}>
      <SalaryAdvanceStack.Screen name={SCREENS.salaryAdvance} component={SalaryAdvanceScreen} />
      <SalaryAdvanceStack.Screen name={SCREENS.rewardPointScreen} component={RewardPointScreen} />
    </SalaryAdvanceStack.Navigator>
  )
}
/**
 * A list of routes from which we're allowed to leave the app when
 * the user presses the back button on Android.
 *
 * Anything not on this list will be a standard `back` action in
 * react-navigation.
 *
 * `canExit` is used in ./app/app.tsx in the `useBackButtonHandler` hook.AndroidSafeArea
 */
const exitRoutes = ['onBoarding']
export const canExit = (routeName: string) => exitRoutes.includes(routeName)

// const styles = StyleSheet.create({
//   AndroidSafeArea: {
//     backgroundColor: 'white',
//     paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0
//   }
// })
