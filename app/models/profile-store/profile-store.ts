import { applySnapshot, cast, flow, getSnapshot, Instance, SnapshotOut, types } from 'mobx-state-tree'
import { withEnvironment } from '../extensions'
import _ from 'lodash'
import { OrderProductStoreModel } from '@app/models'
import DeviceInfo from 'react-native-device-info'
import { baseTimeFormat } from '@app/utils/time'
import { save, load } from '@app/utils/storage'
import { FileUploadResponse } from '@app/services/api/interface/file.interface.ts';
import { UpdateProfileRequest } from '@app/services/api/interface/profile.interface'
import {
  ResetPasscodeWithOtpRequest,
  ResetPasscodeWithOtpResult
} from '@app/services/api/interface/passcode.interface';
/**
 * Model description here for TypeScript hints.
 */

export const HistoryModel = types.model(
  {
    code: types.maybeNull(types.frozen()),
    phone: types.maybeNull(types.string),
    service: types.maybeNull(types.string),
    description: types.maybeNull(types.string),
    status: types.maybeNull(types.frozen()),
    storeAddress: types.maybeNull(types.string),
    storeId: types.maybeNull(types.string),
    storeManagerId: types.maybeNull(types.string),
    storeName: types.maybeNull(types.string),
    timeCheckIn: types.maybeNull(types.frozen()),
    typePet: types.maybeNull(types.frozen()),
    userId: types.maybeNull(types.string),
    weight: types.maybeNull(types.string),
    _id: types.maybeNull(types.string),
    note: types.maybeNull(types.string),
    price: types.maybeNull(types.frozen()),
    branchAddress: types.maybeNull(types.string),
    branchName: types.maybeNull(types.string),
    image: types.maybeNull(types.string),
    branchPhone: types.maybeNull(types.string),
    orderId: types.maybeNull(types.string),
    serviceDetail: types.maybeNull(types.frozen()),
    isPayOnline: types.maybeNull(types.frozen()),
    paymentMethod: types.maybeNull(types.frozen()),
    shortDes: types.maybeNull(types.frozen()),
    bankCode: types.maybeNull(types.frozen()),
    bookingType: types.maybeNull(types.frozen()),
    priceBooking: types.maybeNull(types.frozen()),
    createAt: types.maybeNull(types.frozen()),
  },
)

export const BookingHotelModel = types.model({
  code: types.maybeNull(types.frozen()),
  phone: types.maybeNull(types.string),
  service: types.maybeNull(types.string),
  description: types.maybeNull(types.string),
  status: types.maybeNull(types.frozen()),
  storeAddress: types.maybeNull(types.string),
  storeId: types.maybeNull(types.string),
  storeManagerId: types.maybeNull(types.string),
  storeName: types.maybeNull(types.string),
  timeCheckIn: types.maybeNull(types.frozen()),
  typePet: types.maybeNull(types.frozen()),
  userId: types.maybeNull(types.string),
  weight: types.maybeNull(types.string),
  _id: types.maybeNull(types.string),
  note: types.maybeNull(types.string),
  price: types.maybeNull(types.frozen()),
  branchAddress: types.maybeNull(types.string),
  branchName: types.maybeNull(types.string),
  image: types.maybeNull(types.string),
  branchPhone: types.maybeNull(types.string),
  orderId: types.maybeNull(types.string),
  serviceDetail: types.maybeNull(types.frozen()),
  isPayOnline: types.maybeNull(types.frozen()),
  paymentMethod: types.maybeNull(types.frozen()),
  shortDes: types.maybeNull(types.frozen()),
  bankCode: types.maybeNull(types.frozen()),
  createAt: types.maybeNull(types.frozen()),
},
)

export const BookingClinicModel = types.model(
  {
    code: types.maybeNull(types.frozen()),
    phone: types.maybeNull(types.string),
    service: types.maybeNull(types.string),
    description: types.maybeNull(types.string),
    status: types.maybeNull(types.frozen()),
    storeAddress: types.maybeNull(types.string),
    storeId: types.maybeNull(types.string),
    storeManagerId: types.maybeNull(types.string),
    storeName: types.maybeNull(types.string),
    timeCheckIn: types.maybeNull(types.frozen()),
    typePet: types.maybeNull(types.frozen()),
    userId: types.maybeNull(types.string),
    weight: types.maybeNull(types.string),
    _id: types.maybeNull(types.string),
    note: types.maybeNull(types.string),
    price: types.maybeNull(types.frozen()),
    branchAddress: types.maybeNull(types.string),
    branchName: types.maybeNull(types.string),
    image: types.maybeNull(types.string),
    branchPhone: types.maybeNull(types.string),
    orderId: types.maybeNull(types.string),
    serviceDetail: types.maybeNull(types.frozen()),
    isPayOnline: types.maybeNull(types.frozen()),
    paymentMethod: types.maybeNull(types.frozen()),
    shortDes: types.maybeNull(types.frozen()),
    bankCode: types.maybeNull(types.frozen()),
    createAt: types.maybeNull(types.frozen()),
  },
)

export const AddressModel = types.model(
  {
    _id: types.maybeNull(types.frozen()),
    district: types.maybeNull(types.string),
    province: types.maybeNull(types.string),
    ward: types.maybeNull(types.string),
    street: types.maybeNull(types.string),
    name: types.maybeNull(types.string),
    phone: types.maybeNull(types.string),
    address: types.maybeNull(types.string),
    default: types.optional(types.boolean, false),
  },
)
export const BankModel = types.model(
  {
    bankType: types.maybeNull(types.string),
    bankName: types.maybeNull(types.string),
    bankCode: types.maybeNull(types.string),
    ownerName: types.maybeNull(types.string),
    bankAccountNumber: types.maybeNull(types.string)
  },
)

export const UpdateProfileModel = types.model(
  {
    _id: types.maybeNull(types.string),
    uid: types.maybeNull(types.string),
    photoUrl: types.maybeNull(types.string),
    email: types.maybeNull(types.string),
    displayName: types.maybeNull(types.string),
    isLocked: types.maybeNull(types.boolean),
    phoneNumber: types.maybeNull(types.string),
    password: types.maybeNull(types.string),
    error: types.maybeNull(types.boolean),
    fullName: types.maybeNull(types.string),
    phone: types.maybeNull(types.string),
    picture: types.maybeNull(types.string),
    avatarUser: types.maybeNull(types.string),
    birthday: types.maybeNull(types.string),
    sex: types.maybeNull(types.string),
    description: types.maybeNull(types.string),
    namePet: types.maybeNull(types.string),
    location: types.maybeNull(types.string),
    addressList: types.optional(types.array(AddressModel), []),
    branchPhone: types.maybeNull(types.string),
    fcmToken: types.maybeNull(types.string),
    status: types.maybeNull(types.frozen()),
    name: types.maybeNull(types.frozen()),
  },
)

export const ContactInfoModel = types.model({
  fullName: types.maybeNull(types.string),
  position: types.maybeNull(types.string),
  phoneNumber: types.maybeNull(types.string),
  email: types.maybeNull(types.string),
})

export const ContractModel = types.model({
  contractNo: types.maybeNull(types.string),
  signedDate: types.maybeNull(types.number),
  attachedFile: types.maybeNull(types.string),
})

export const PartnerAccountModel = types.model({
  bankType: types.maybeNull(types.string),
  bankName: types.maybeNull(types.string),
  bankCode: types.maybeNull(types.string),
  ownerName: types.maybeNull(types.string),
  bankAccountNumber: types.maybeNull(types.string),
})

export const PartnerAddressModel = types.model({
  province: types.maybeNull(types.string),
  district: types.maybeNull(types.string),
  ward: types.maybeNull(types.string),
  address: types.maybeNull(types.string),
})

export const PartnerModel = types.model({
  id: types.maybeNull(types.number),
  name: types.maybeNull(types.string),
  shortName: types.maybeNull(types.string),
  taxCode: types.maybeNull(types.string),
  address: types.optional(types.array(PartnerAddressModel), []),
  foundingDate: types.maybeNull(types.number),
  email: types.maybeNull(types.string),
  phoneNumber: types.maybeNull(types.string),
  contactInfos: types.optional(types.array(ContactInfoModel), []),
  contract: types.maybeNull(ContractModel),
  status: types.maybeNull(types.string),
  standardWorkday: types.maybeNull(types.number),
  advanceLockDate: types.maybeNull(types.number),
  repaymentDeadline: types.maybeNull(types.number),
  salaryPaymentDate: types.maybeNull(types.number),
  note: types.maybeNull(types.string),
  canEditAccount: types.maybeNull(types.boolean),
  accounts: types.optional(types.array(PartnerAccountModel), []),
  feeIds: types.optional(types.array(types.number), []),
  creditLimit: types.maybeNull(types.number),
})

export const ProfileStoreModel = types
  .model('ProfileStore')
  .props({
    _id: types.maybeNull(types.string),
    uid: types.maybeNull(types.string),
    photoUrl: types.maybeNull(types.string),
    email: types.maybeNull(types.string),
    displayName: types.maybeNull(types.string),
    isLocked: types.maybeNull(types.boolean),
    phoneNumber: types.maybeNull(types.string),
    password: types.maybeNull(types.string),
    username: types.maybeNull(types.string),
    status: types.maybeNull(types.string),
    isValidForm: types.maybeNull(types.boolean),
    error: types.maybeNull(types.boolean),
    fullName: types.maybeNull(types.string),
    phone: types.maybeNull(types.string),
    picture: types.maybeNull(types.string),
    avatarUser: types.maybeNull(types.string),
    birthday: types.maybeNull(types.string),
    sex: types.maybeNull(types.string),
    description: types.maybeNull(types.string),
    namePet: types.maybeNull(types.string),
    location: types.maybeNull(types.string),
    addressList: types.optional(types.array(AddressModel), []),
    dataUserBank: types.optional(types.array(BankModel), []),
    typePet: types.maybeNull(types.string),
    bookingHistory: types.optional(types.array(types.frozen()), []),
    // bookingHotel: types.optional(types.array(types.frozen()), []),
    bookingHotel: types.optional(types.array(BookingHotelModel), []),
    bookingClinic: types.optional(types.array(BookingClinicModel), []),
    bookingShowroomData: types.optional(types.array(types.frozen()), []),
    pointHistory: types.optional(types.array(types.frozen()), []),
    branchPhone: types.maybeNull(types.string),
    totalPage: types.maybeNull(types.number),
    fcmToken: types.maybeNull(types.string),
    point: types.maybeNull(types.number),
    isPayOnline: types.maybeNull(types.string),
    paymentMethod: types.maybeNull(types.string),
    reloadStatus: types.maybeNull(types.boolean),
    reloadData: types.maybeNull(types.boolean),
    refreshAddress: types.maybeNull(types.boolean),
    name: types.maybeNull(types.frozen()),
    identifierNo: types.maybeNull(types.string),
    employeeId: types.maybeNull(types.string),  
    standardWorkDay: types.maybeNull(types.number),
    timesheetHistory: types.maybeNull(types.frozen()),
    incomeData: types.maybeNull(types.frozen()),
    partner: types.maybeNull(PartnerModel),
    // Passcode fields
    passcodeExpiryDate: types.maybeNull(types.string),
    passcodeFailedAttempts: types.maybeNull(types.number),
    passcodeLastResetDate: types.maybeNull(types.string), 
    passcodeEnabled: types.maybeNull(types.boolean),
  })
  .extend(withEnvironment)
  .views((self) => ({
    checkInvalid: function () {
      self.isValidForm = self.displayName !== undefined && self.email !== undefined
      __DEV__ && console.log('self.isValidForm', self.isValidForm)
    },
    getIsValidForm: function () {
      return !self.isValidForm
    },
  }))

  .actions((self) => ({
    getProfile: flow(function* () {
      __DEV__ && console.log('get Profile action')
      const result: any = yield self.environment.api.getProfileInfo()
      if (result && result.data) {
        const user = result.data
        if (user) {
          if (user.address && user.address?.length) {
            const dataAddress: typeof AddressModel[] = user.address.map(item => {
              return {
                ...item,
                province: item.city
              }
            })
            self.addressList = cast(dataAddress)
          }
          if (user.bankAccounts && user.bankAccounts?.length) {
            const dataBank: typeof BankModel[] = user.bankAccounts.map(item => {
              return {
                ...item
              }
            })
            self.dataUserBank = cast(dataBank)
          } else {
            self.dataUserBank = cast([])
          }
          // save to db
          self.fullName = cast(user.name || user.username || '')
          self.phone = cast(user.phoneNumber || user.username || '')
          self.avatarUser = cast(user.avatar)
          self.email = cast(user.email)
          self.birthday = cast(baseTimeFormat(user.dateOfBirth, 'YYYY-MM-DD'))
          self.identifierNo = cast(user.identifierNo)
          self.employeeId = cast(user.employeeId ? String(user.employeeId) : null)
          self.username = cast(user.username)
          self.partner = cast(user.partner)
          self.status = cast(user.status)
          self._id = cast(user.id)

          // Save passcode info
          self.passcodeExpiryDate = cast(user.passcodeExpiryDate || null)
          self.passcodeFailedAttempts = cast(user.passcodeFailedAttempts || 0)
          self.passcodeLastResetDate = cast(user.passcodeLastResetDate || null)
          self.passcodeEnabled = cast(user.passcodeEnabled || false)
          //todo: lấy passcode
        }
      }
      return result
    })
  }))
  .actions((self) => ({
    updateProfile: flow(function* (body: UpdateProfileRequest) {
      if (!body) return
      const result: any = yield self.environment.api.updateProfile(body)
      if (result?.data?.data) {
        // const token = result.data.data.token
        // yield saveToken(token)
        self.getProfile()
      }
      return result
    })
  }))
  .actions((self) => ({
    uploadImage: flow(function* (url, files) {
      const result: FileUploadResponse = yield self.environment.api.uploadImages(url, files)
      __DEV__ && console.log('uploadImage OK', result)
      if (result && result.data.details) {
        self.avatarUser = cast(String(result.data.details.id))
        self.updateProfile({ avatar: String(self.avatarUser) })
      } else {
        __DEV__ && console.log(result)
      }
    }),
    updateTokenNotification: flow(function* (token = null) {
      const fcmTk = token || self.fcmToken
      if (fcmTk) {
        // Get previously saved token from local storage
        const savedToken = yield load("FCM_TOKEN")
        
        // Update the token in the store
        self.fcmToken = cast(fcmTk)        
        // Save current token to local storage
        yield save("FCM_TOKEN", fcmTk)
        
        // Only call API if token is different from the saved one
        if (savedToken !== fcmTk) {
          __DEV__ && console.log('Token changed, calling api saveTokenFirebase')
          const body = {
            deviceToken: fcmTk,
            deviceType: String(DeviceInfo.getSystemName()).toUpperCase(), // Lấy hệ điều hành (ví dụ: "iOS", "Android")
            deviceInfo: {
              version: DeviceInfo.getSystemVersion(), // Lấy phiên bản hệ điều hành (ví dụ: "14.4")
              model: DeviceInfo.getModel(), // Lấy mẫu thiết bị (ví dụ: "iPhone 12")
              manufacturer: DeviceInfo.getManufacturerSync(), // Lấy nhà sản xuất (ví dụ: "Apple")
            }
          }
          return yield self.environment.api.saveTokenFirebase(body)
        } else {
          __DEV__ && console.log('Token unchanged, skipping API call')
        }
      } else {
        __DEV__ && console.log('tokenNotification local is null')
      }
    }),
    getBookingHistory: flow(function* (page, isLoadMore) {
      const result: any = yield self.environment.api.getBookingHistory(page)
      if (result && result.data) {
        const data = result.data
        const dataBookingHistory: typeof HistoryModel[] = data.lichsu.map(item => {
          return {
            ...item
          }
        })
        if (isLoadMore) {
          const tempArray = [...self.bookingHistory, ...dataBookingHistory]
          _.uniqBy(tempArray, '_id')
          self.bookingHistory = cast(tempArray)
        } else {
          self.bookingHistory = cast(dataBookingHistory)
        }
        self.totalPage = cast(data.totalPage || 0)
      } else {
        __DEV__ && console.log('result.data', result.data)
      }
    }),
  }))

  .actions((self) => ({
    getBookingHotel: flow(function* (page, isLoadMore) {
      const result: any = yield self.environment.api.getBookingHotel(page)
      if (result && result.data) {
        const data = result.data
        const dataBookingHotel = data.lichsu.map(item => {
          return {
            ...item
          }
        })
        if (isLoadMore) {
          const tempArray = [...self.bookingHotel, ...dataBookingHotel]
          _.uniqBy(tempArray, '_id')
          self.bookingHotel = cast(tempArray)
        } else {
          self.bookingHotel = cast(dataBookingHotel)
        }
        self.totalPage = cast(data.totalPage || 0)
      } else {
        __DEV__ && console.log('result.data', result.data)
      }
    }),
  }))
  .actions((self) => ({
    getBookingClinic: flow(function* (page, isLoadMore) {
      const result: any = yield self.environment.api.getBookingClinic(page)
      if (result && result.data) {
        const data = result.data
        const dataBookingClinic: typeof BookingClinicModel[] = data.lichsu.map(item => {
          return {
            ...item
          }
        })
        if (isLoadMore) {
          const tempArray = [...self.bookingClinic, ...dataBookingClinic]
          _.uniqBy(tempArray, '_id')
          self.bookingClinic = cast(tempArray)
        } else {
          self.bookingClinic = cast(dataBookingClinic)
        }
        self.totalPage = cast(data.totalPage || 0)
      } else {
        __DEV__ && console.log('result.data', result.data)
      }
    }),
    getBookingShowRoom: flow(function* (page, isLoadMore) {
      const result: any = yield self.environment.api.getBookingClassification(page)
      if (result && result?.data?.data) {
        const data = result.data.data
        const dataArr = data.lichsu.map(item => {
          return {
            ...item
          }
        })
        if (isLoadMore) {
          const tempArray = [...self.bookingShowroomData, ...dataArr]
          _.uniqBy(tempArray, '_id')
          self.bookingShowroomData = cast(tempArray)
        } else {
          self.bookingShowroomData = cast(dataArr)
        }
        self.totalPage = cast(data.totalPage || 0)
      } else {
        __DEV__ && console.log('result.data', result.data)
      }
    }),
  }))
  .actions((self) => ({
    getUserPointHistory: flow(function* () {
      const result: any = yield self.environment.api.getUserPointHistory()
      if (result && result.data) {
        const dataArr = result.data.data.records.map(item => {
          return {
            ...item
          }
        })
        self.pointHistory = cast(dataArr)
      } else {
        __DEV__ && console.log('result.data', result.data)
      }
    }),
  }))

  //Employee section
  .actions((self) => ({
    getLichSuChamCong: flow(function* (params) {
      const result: any = yield self.environment.api.getLichSuChamCong(params)
      if (result && result.data) {
        self.standardWorkDay = result.data.standardWorkDay
      }
      return result
    }),
    getBangChamCong: flow(function* (params) {
      const result: any = yield self.environment.api.getBangChamCong(params)
      if (result && result.data) {
        self.timesheetHistory = result.data.timesheetHistory
      }
      return result
    }),
    getIncome: flow(function* (params) {
      const result: any = yield self.environment.api.getIncome(params)
      if (result && result.data) {
        self.incomeData = result.data.details
      }
      return result
    }),

    /**
     * Reset passcode using OTP verification
     */
    resetPasscodeWithOtp: flow(function* (data: ResetPasscodeWithOtpRequest): Generator<any, ResetPasscodeWithOtpResult, any> {
      const result = yield self.environment.api.resetPasscodeWithOtp(data)
      if (result && result.kind === 'ok') {
        // Refresh profile to get updated passcode info
        yield self.getProfile()
      }
      return result
    }),
  }))

  .views((self) => ({
    isSignedIn: function () {
      return !!self._id
    }
  }))
  .actions((self) => ({
    setReLoadStatus: function (value) {
      self.reloadStatus = value
    },
    setDisplay: function (value) {
      self.displayName = value
    },
    setEmail: function (value) {
      self.email = value
    },
    setBookingSpa: function (value) {
      self.bookingHistory = value
    },
    setBookingHotel: function (value) {
      self.bookingHotel = value
    },
    setBookingClinic: function (value) {
      self.bookingClinic = value
    },
    setPassword: function (value) {
      self.password = value
    },
    setBirthDay: function (value) {
      self.birthday = value
    },
    setPicture: function (value) {
      self.picture = value
    },
    setAvatarUser: function (value) {
      self.avatarUser = value
    },
    setFullName: function (value) {
      self.fullName = value
    },
    setPhone: function (value) {
      self.phone = value
    },
    setListAddress: function (address) {
      self.addressList.push(address)
    },
    setReloadData: function (value) {
      self.reloadData = value
    },
    setRefreshAddress: function (value) {
      self.refreshAddress = value
    },
    updateAddressOfList: function (address, index = -1) {
      if (address.default) {
        // reset default
        self.addressList.forEach(item => {
          item.default = false
        })
      }
      // Find index of specific object using findIndex method.
      const objIndex = index > -1 ? index : self.addressList.findIndex(obj => obj._id === address._id)
      // Update object's name property.
      self.addressList[objIndex].district = address.district
      self.addressList[objIndex].province = address.province
      self.addressList[objIndex].ward = address.ward
      self.addressList[objIndex].street = address.street
      self.addressList[objIndex].name = address.name
      self.addressList[objIndex].phone = address.phone
      self.addressList[objIndex].address = address.address
      self.addressList[objIndex].default = address.default
    },
    deleteAddressOfList: function (index) {
      // const index = self.addressList.findIndex(x => x._id === id)
      const cloneArray = [...self.addressList]
      cloneArray.splice(index, 1)
      self.addressList = cast([...cloneArray])
    },
    clearFields: function () {
      self.fullName = ''
      self.phone = ''
      self.picture = ''
      self.photoUrl = ''
      self.email = ''
      self.birthday = ''
      self.password = ''
      self.fullName = ''
      self.sex = ''
      self.description = ''
      self.namePet = ''
      self.typePet = ''
      self.location = ''
      self._id = cast('')
      self.addressList = cast([])
    },
    setPhoneNumber: function (phone: string) {
      // validate đủ 10 số
      if (phone.length && _.startsWith(phone, '+840')) {
        const search = '+840'
        const replaceWith = '+84'
        self.phoneNumber = phone.replace(search, replaceWith)
      } else {
        self.phoneNumber = phone
      }
      // self.checkInvalid()
    },
  }))
  .actions(self => {
    return {
      afterCreate: () => {
      },
      reset: () => {
        applySnapshot(self, {})
      },
      logout: flow(function* () {
        try {
          yield self.environment.api.logoutUser();
        } catch (error) {
          console.error('Logout error:', error);
        }
      }),
    }
  })

/**
 * Un-comment the following to omit model attributes from your snapshots (and from async storage).
 * Useful for sensitive data like passwords, or transitive state like whether a modal is open.

 * Note that you'll need to import `omit` from ramda, which is already included in the project!
 *  .postProcessSnapshot(omit(["password", "socialSecurityNumber", "creditCardNumber"]))
 */

type ProfileStoreType = Instance<typeof ProfileStoreModel>

export interface ProfileStore extends ProfileStoreType {
}

type ProfileStoreSnapshotType = SnapshotOut<typeof ProfileStoreModel>

export interface ProfileStoreSnapshot extends ProfileStoreSnapshotType {
}
