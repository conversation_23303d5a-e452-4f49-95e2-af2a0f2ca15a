import { StyleSheet } from 'react-native'
import { color, spacing, typography } from '@app/theme'

export default StyleSheet.create({
  imgBackground: {
    flex: 1,
    width: '100%',
    height: '100%',
    backgroundColor: '#F5F5F5'
  },
  container: {
    alignItems: 'center',
    backgroundColor: '#fff',
    flex: 1,
    justifyContent: 'space-between',
    marginHorizontal: 30
  },
  content: {
    flex: 1,
    width: '100%',
    marginTop: 20
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.small,
    backgroundColor: color.transparent
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    fontFamily: typography.normal
  },
  description: {
    fontSize: 14,
    color: '#333',
    marginBottom: spacing.medium,
    lineHeight: 20,
    fontFamily: typography.normal
  },
  formGroup: {  
    width: '100%',
    marginTop: 16
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    fontFamily: typography.normal
  },
  input: {
    marginTop: 8,
  },
  button: {
    marginTop: spacing.large,
    backgroundColor: '#0A5936',
    width: '100%'
  },
  scrollView: {
    flex: 1,
    width: '100%'
  },
  buttonLogin: {
    alignItems: 'center',
    flexDirection: 'row',
    height: 44,
    justifyContent: 'center',
    backgroundColor: '#0A5936',
    width: '100%'
  },
  noteContainer: {
    flexDirection: 'row',
    backgroundColor: '#F8F8F8',
    padding: 10,
    borderRadius: 8,
    alignItems: 'flex-start',
    width: '100%',
    marginTop: spacing.medium
  },
  noteText: {
    fontSize: 13,
    color: '#666666',
    marginLeft: spacing.small,
    fontFamily: typography.normal,
    flex: 1,
    lineHeight: 18
  },
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
    marginTop: -4
  },
  icArrowBack: {
    margin: 11,
  },
  textTitle: {
    color: '#000811',
    fontFamily: typography.normal,
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10
  },
  mainTextInput: {
    marginTop: 0,
    width: '100%',
  },
  buttonContainer: {
    marginTop: 15,
    width: '100%'
  },
  passcodeContainer: {
    width: '100%',
    marginTop: 8,
  },
  hiddenInput: {
    position: 'absolute',
    width: 0,
    height: 0,
    opacity: 0,
  },
  digitsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  digitBox: {
    width: 45,
    height: 45,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: color.primary,
    backgroundColor: color.palette.white,
  },
  digitInput: {
    width: '100%',
    height: '100%',
    fontSize: 24,
    color: 'green',
    fontWeight: 'bold',
    padding: 0,
  },
  digitBoxContainer: {
    width: 45,
    height: 45,
    position: 'relative',
  },
  digitText: {
    fontSize: 20,
    color: color.text,
  },
  dotText: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    textAlign: 'center',
    lineHeight: 45,
    fontSize: 24,
    color: 'green',
  },
  passwordInput: {
    marginTop: 4
  },
})
