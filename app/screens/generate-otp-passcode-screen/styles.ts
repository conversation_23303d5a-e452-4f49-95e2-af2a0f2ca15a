import { StyleSheet } from 'react-native'
import { color, spacing, typography } from '@app/theme'

export default StyleSheet.create({
  safeAreaView: {
    backgroundColor: '#fff',
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 30,
    paddingTop: 20,
  },
  textTitle: {
    color: '#000811',
    fontFamily: typography.normal,
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  viewTextContent: {
    marginTop: 20,
    marginBottom: 30,
  },
  text: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    fontFamily: typography.normal,
    textAlign: 'center',
  },
  phoneContainer: {
    backgroundColor: '#F8F8F8',
    padding: 15,
    borderRadius: 8,
    marginBottom: 30,
    alignItems: 'center',
  },
  phoneLabel: {
    fontSize: 14,
    color: '#666',
    fontFamily: typography.normal,
    marginBottom: 5,
  },
  phoneNumber: {
    fontSize: 16,
    color: '#000',
    fontFamily: typography.normal,
    fontWeight: '600',
  },
  buttonContainer: {
    marginTop: 20,
    width: '100%',
  },
})
