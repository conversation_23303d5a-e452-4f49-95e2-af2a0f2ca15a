import { SafeAreaView } from 'react-native-safe-area-context'
import { Text, View } from 'react-native'
import React, { useContext, useEffect, useState } from 'react'
import styles from './styles'
import { observer } from 'mobx-react-lite'
import { useStores } from '@app/models'
import { useNavigation } from '@react-navigation/native'
import { SCREENS } from '@app/navigation'
import { useTranslation } from 'react-i18next'
import { TButton, ButtonBack } from '../../components'
import { ModalContext } from '@app/context'

export const GenerateOtpPasscodeScreen = observer(() => {
  const { t }: any = useTranslation()
  const { accountStore, profileStore } = useStores()
  const { navigate, goBack } = useNavigation<any>()
  const { showError, showSuccess } = useContext(ModalContext)
  const [isSubmitting, setSubmitting] = useState(false)

  useEffect(() => {
    // Check if user is logged in
    if (!accountStore.isLogin()) {
      showError('', t('<PERSON>ui lòng đăng nhập để thay đổi mã passcode'))
      navigate(SCREENS.login)
      return
    }
  }, [])

  const handleGenerateOtp = async () => {
    try {
      setSubmitting(true)
      
      // Get user's phone number from profile
      const phoneNumber = profileStore.phone || profileStore.phoneNumber
      
      if (!phoneNumber) {
        showError(t('FAIL'), t('Không tìm thấy số điện thoại trong hồ sơ'))
        setSubmitting(false)
        return
      }

      // Generate OTP for passcode reset
      const result = await accountStore.generateOtp(
        phoneNumber,
        'RESET_PASSCODE',
        'ZNS',
        'Xác thực đặt lại mã passcode'
      )

      if (result.kind === 'ok' && result.data?.success) {
        showSuccess(t('THANHCONG'), t('Đã gửi mã OTP đến số điện thoại của bạn'))
        // Navigate to OTP verification screen
        navigate(SCREENS.changePasscodeViaOtpScreen, {
          phoneNumber: phoneNumber,
          otpType: 'RESET_PASSCODE'
        })
      } else {
        // Handle different error cases
        if (result.kind === 'bad-data') {
          showError(t('FAIL'), t('Có lỗi xảy ra khi gửi mã OTP'))
        } else if (result.kind === 'server' || result.kind === 'unauthorized' || result.kind === 'forbidden') {
          showError(t('FAIL'), result.data?.message || t('Lỗi máy chủ, vui lòng thử lại sau'))
        } else if (result.kind === 'ok' && !result.data?.success) {
          showError(t('FAIL'), result?.data?.message || t('Không thể gửi mã OTP'))
        } else {
          showError(t('FAIL'), result?.data?.message || t('Có lỗi xảy ra khi gửi mã OTP'))
        }
      }
    } catch (error) {
      console.log('Generate OTP error:', error)
      showError(t('FAIL'), t('Có lỗi xảy ra, vui lòng thử lại sau'))
    } finally {
      setSubmitting(false)
    }
  }

  function onBack() {
    goBack()
  }

  // If user is not logged in, don't render the UI
  if (!accountStore.isLogin()) {
    return null
  }

  return (
    <SafeAreaView style={styles.safeAreaView} edges={['right', 'top', 'left']}>
      <View style={styles.container}>
        <ButtonBack onPress={onBack} />
        <View style={styles.content}>
          <View>
            <View>
              <Text numberOfLines={2} style={styles.textTitle}>
                {t('Thay đổi mã passcode')}
              </Text>
            </View>
            <View style={styles.viewTextContent}>
              <Text numberOfLines={3} style={styles.text}>
                {t('Chúng tôi sẽ gửi mã OTP đến số điện thoại đã đăng ký để xác thực việc thay đổi mã passcode của bạn.')}
              </Text>
            </View>
            <View style={styles.phoneContainer}>
              <Text style={styles.phoneLabel}>{t('Số điện thoại:')}</Text>
              <Text style={styles.phoneNumber}>
                {profileStore.phone || profileStore.phoneNumber || 'Chưa có số điện thoại'}
              </Text>
            </View>
            <View style={styles.buttonContainer}>
              <TButton 
                disabled={isSubmitting || !profileStore.phone} 
                loading={isSubmitting} 
                title={t('Gửi mã OTP')} 
                onPress={handleGenerateOtp} 
              />
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  )
})
