import {
  Text,
  View,
  ScrollView,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform
} from 'react-native'
import React, { useState, useEffect, useContext } from 'react'
import { useTranslation } from 'react-i18next'
import { useStores } from '../../models/root-store'
import { useNavigation } from '@react-navigation/native'
import { observer } from 'mobx-react-lite'
import { ButtonBack, TTextInput, TButton } from '@app/components'
import { color } from '@app/theme'
import { Api } from '@app/services/api'
import styles from '@app/screens/passcode-screen/styles'
import { ModalContext } from '@app/context'
import Icon from 'react-native-vector-icons/Ionicons'
import { PasswordInput } from '@app/components/password-input.tsx';

const api = new Api()

export const PasscodeScreen = observer(function PasscodeScreen() {
  const { t } = useTranslation()
  const navigation = useNavigation<any>()
  const { accountStore, profileStore } = useStores()
  const [oldPasscode, setOldPasscode] = useState('')
  const [passcode, setPasscode] = useState('')
  const [confirmPasscode, setConfirmPasscode] = useState('')
  const [loading, setLoading] = useState(false)
  const { showError, showSuccess, showCustomSuccess, showModal, showCustomError } = useContext(ModalContext)

  const goBack = () => navigation.goBack()
  const onBack = () => navigation.goBack()

  // Check if user is logged in
  useEffect(() => {
    if (!accountStore.isLogin()) {
      showError('',t('Vui lòng đăng nhập để thiết lập mã passcode'))
      navigation.navigate('Login')
    }
  }, [])

  const validatePasscode = () => {
    if (profileStore.passcodeEnabled) {
      if (!oldPasscode) {
        showError('',t('Vui lòng nhập mã passcode cũ'))
        return false
      }
    }
    if (!passcode) {
      showError('',t('Vui lòng nhập mã passcode mới'))
      return false
    }
    if (passcode.length < 6) {
      showError('',t('Mã passcode phải đủ 6 ký tự'))
      return false
    }
    if (passcode !== confirmPasscode) {
      showError('',t('Mã passcode và xác nhận không khớp'))
      return false
    }
    return true
  }

  const setupPasscode = async () => {
    if (!validatePasscode()) return
    try {
      setLoading(true)
      const response = await api.setupPasscode({
        oldPasscode: profileStore?.passcodeEnabled ? oldPasscode : undefined,
        passcode,
        confirmPasscode
      })
      if (response.kind === 'ok' && response.data) {
        showSuccess('', t('Thiết lập mã passcode thành công'))
        profileStore.getProfile()
        goBack()
      } else {
        showError('',response?.data?.data?.message || t('Có lỗi xảy ra, vui lòng thử lại sau'))
      }
    } catch (error) {
      console.log('Setup passcode error:', error)
      showError('',t('Có lỗi xảy ra, vui lòng thử lại sau'))
    } finally {
      setLoading(false)
    }
  }

  // If user is not logged in, don't render the passcode setup UI
  if (!accountStore.isLogin()) {
    return null
  }

  return (
    <SafeAreaView style={styles.safeAreaView}>
      <ButtonBack onPress={onBack} style={styles.icArrowBack} />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView
          keyboardShouldPersistTaps="handled"
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.content}>
            <View>
              <Text style={styles.textTitle}>
                {profileStore.passcodeEnabled ? t('Thay đổi mã passcode') : t('Thiết lập mã passcode')}
              </Text>
              <Text style={styles.description}>
                {profileStore.passcodeEnabled
                  ? t('Nhập mã passcode cũ và mã mới để thay đổi.')
                  : t('Thiết lập mã passcode để bảo mật các giao dịch quan trọng của bạn.')}
              </Text>
            </View>

            <View style={styles.mainTextInput}>
              {profileStore.passcodeEnabled && (
                <View style={styles.formGroup}>
                  <Text style={styles.label}>{t('Mã passcode cũ')}</Text>
                  <PasswordInput
                    value={oldPasscode}
                    onChange={setOldPasscode}
                    placeholder={t('Nhập mã passcode cũ')}
                    style={styles.input}
                    keyboardType="numeric"
                    maxLength={6}
                  />
                </View>
              )}

              <View style={styles.formGroup}>
                <Text style={styles.label}>{t('Mã passcode mới')}</Text>
                <PasswordInput
                  value={passcode}
                  onChange={setPasscode}
                  placeholder={t('Nhập mã passcode mới')}
                  style={styles.input}
                  keyboardType="numeric"
                  maxLength={6}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>{t('Xác nhận mã passcode mới')}</Text>
                <PasswordInput
                  value={confirmPasscode}
                  onChange={setConfirmPasscode}
                  placeholder={t('Nhập lại mã passcode mới')}
                  style={styles.input}
                  keyboardType="numeric"
                  maxLength={6}
                />
              </View>

              <View style={styles.noteContainer}>
                <View>
                  {/* @ts-ignore */}
                  <Icon name="information-circle-outline" size={20} color={color.primary} style={{ marginRight: 5 }} />
                </View>
                <Text style={styles.noteText}>
                  {t('Mã passcode phải đủ 6 ký tự')}
                </Text>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <TButton
                buttonStyle={styles.button}
                title={profileStore.passcodeEnabled ? t('Cập nhật') : t('Thiết lập')}
                onPress={setupPasscode}
                loading={loading}
                typeRadius={'rounded'}
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
})
