import {

  Text,
  View,
  TouchableOpacity, Linking,
  ImageBackground,
  <PERSON>rollView,
  RefreshControl
} from 'react-native'
import React, { useState, Fragment, useEffect, useRef } from 'react'
import styles from './styles'
import { useTranslation } from 'react-i18next'
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'
import { useStores } from '@app/models'
import { CommonActions, useNavigation } from '@react-navigation/native'
import { Modalize } from 'react-native-modalize'
import { observer } from 'mobx-react-lite'
import { SCREENS } from '@app/navigation'
import { useAuth } from '@app/use-hooks/use-auth'
import Share from 'react-native-share'
import { LazyImage, ButtonBack, ConfirmDialog } from '@app/components'
import DeviceInfo from 'react-native-device-info'
import { loadString } from '@app/utils/storage'
import { LogEvent } from '@app/services/loggingServices'
import { color, typography } from '@app/theme'
import { Api } from '@app/services/api'
import { isAndroid } from '@app/utils/chat/deviceInfo'
import { APP_STORE_LINK, PLAY_MARKET_LINK } from '@app/constants/links'
import { bgConfirmAdvanceSalary } from '@app/assets/images'
import FastImage from 'react-native-fast-image';
// import { ModalContext } from '@app/context'
const api = new Api()

// const { width, height } = Dimensions.get('window')

export const ProfileUserScreen = observer((props) => {
  const { t }: any = useTranslation()
  const { navigate, goBack } = useNavigation<any>()
  const navigation = useNavigation<any>()
  const { profileStore, notificationStore, carStore, productStore } = useStores()
  // const [refreshing, setRefreshing] = useState(false)
  const modalizeRef = useRef<Modalize>(null)
  // const [photoUrl, setPhotoUrl] = useState("")
  const { signOut, userToken } = useAuth() // should be signUp
  // const { showError, showSuccess, showConfirm } = useContext(ModalContext)
  const version = DeviceInfo.getVersion()
  const [versionCodePush, setVersionCodePush] = useState('')
  const [isShowConfirmLogOut, setIsShowConfirmLogOut] = useState(false)

  useEffect(() => {
    loadData().then(r => {
    })
    __DEV__ && console.log('state =>>>>', userToken)
    // setTimeout(forceUpdate, 2000)
    // navigation.addListener('focus', () => {
    //   loadData().then(r => {
    //   })
    // })
  }, [])

  const goLoginScreenRequired = () => {
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }

  const goToEditProfile = () => {
    if (profileStore.isSignedIn()) {
      navigate(SCREENS.editProfile, { title: t('BOOKING_HISTORY') })
    } else {
      goLoginScreenRequired()
      LogEvent('user_click_btn_register', { screen: 'profile_screen' })
    }
  }

  const loadData = async () => {
    if (!profileStore.isSignedIn()) return
    const rs = await profileStore.getProfile()
    if (!userToken) {
      signOut(true)
    }
    const versionData = await loadString('versionCodePush')
    setVersionCodePush(versionData)
  }

  const onOpenModalEditLanguage = () => {
    modalizeRef.current?.open()
  }

  const onCloseModel = () => {
    modalizeRef.current?.close()
  }

  const navigatePetManagemant = () => {
    if (profileStore.isSignedIn()) {
      navigate(SCREENS.carManagement)
    } else {
      goLoginScreenRequired()
      LogEvent('user_click_btn_register', { screen: 'profile_screen' })
    }
  }

  const navigateToSettingProfile = () => {
    if (profileStore.isSignedIn()) {
      navigate(SCREENS.editProfile)
    } else {
      goLoginScreenRequired()
      LogEvent('user_click_btn_register', { screen: 'profile_screen' })
    }
  }

  const goLogin = async () => {
    profileStore.clearFields()
    navigate(SCREENS.authStack, { screen: SCREENS.login })
  }
  const goFastLogin = async () => {
    profileStore.clearFields()
    navigate(SCREENS.authStack, { screen: SCREENS.fastLogin })
  }
  const goRegister = async () => {
    profileStore.clearFields()
    navigate(SCREENS.authStack, { screen: SCREENS.register })
  }

  const goLuckyWheel = async () => {
    navigate(SCREENS.luckyWheelStack)
  }

  const renderHeaderModalize = () => (
    <View style={styles.modal__header}>
      <ButtonBack onPress={onCloseModel} style={styles.viewTouchButtonTop} />
      <Text style={styles.textTitleHeader}>{t('CHON_NGON_NGU')}</Text>
    </View>
  )

  const onInviteFriend = () => {
    const options = {
      title: '',
      social: Share.Social.SMS,
      failOnCancel: false,
      urls: ['https://tgone.vn'],
    }

    Share.open(options)
      .then((res) => {
        __DEV__ && console.log(res)
      })
      .catch((err) => {
        __DEV__ && err && console.log(err)
      })
  }

  const showAlert = () => {
    setIsShowConfirmLogOut(true)
  }

  const updateNow = () => {
    const link = isAndroid ? PLAY_MARKET_LINK : APP_STORE_LINK
    Linking.canOpenURL(link).then(
      (supported) => {
        supported && Linking.openURL(link)
      },
      (err) => console.log(err)
    )
  }

  const renderHeader = () => {
    return (
      <View style={styles.header}>
        <ButtonBack style={{ color: '#333' }} onPress={goBack} />
        <Text style={{ color: '#333', fontWeight: '500', fontSize: 18, fontFamily: typography.normal }}>Thông tin cá nhân</Text>
        <View style={{ width: 24 }} />
      </View>
    )
  }

  const renderInfo = () => {
    return (
      <View style={styles.boxView}>

        <RendeMenuItem screen={SCREENS.notificationScreen} text={t('THONG_BAO')} icon={'bell-circle-outline'} requireLogin={true} />
        {/*<RendeMenuItem screen={SCREENS.chamCong} text={'Chấm công'} icon={'bell-circle-outline'} />*/}
        {profileStore.isSignedIn() && <RendeMenuItem screen={SCREENS.passcodeScreen} text={profileStore.passcodeEnabled ? t('Thay đổi mã passcode') : t('Thiết lập mã passcode')} icon={'shield-lock-outline'} />}
        {profileStore.isSignedIn() && <RendeMenuItem screen={SCREENS.changePasswordScreen} text={t('Thay đổi mật khẩu')} icon={'lock-outline'} />}
        {profileStore.isSignedIn() && <RendeMenuItem screen={SCREENS.changePasswordScreen} text={t('Thay đổi mật khẩu')} icon={'lock-outline'} />}
        {/* <RendeMenuItem screen={SCREENS.createPasswordNewAccount} requireLogin={false} text={t('Test')} icon={'lock-outline'} /> */}
        <RendeMenuItem screen={SCREENS.supportStack} text={t('TAIKHOAN_trogiup')} icon={'lifebuoy'} requireLogin={false} />
        <RendeMenuItem onPress={onInviteFriend} text={t('MOI_BAN_BE')} icon={'account-plus-outline'} requireLogin={false} />
        {profileStore.isSignedIn() && <RendeMenuItem onPress={showAlert} text={t('DANGXUAT')} icon={'logout'} />}
        <View>
          {/* <Text style={styles.textVersion}>{t('VERSION')}{version} - {versionCodePush}</Text> */}
          <Text style={styles.textVersion}>{t('VERSION')}{version}</Text>
        </View>
      </View>
    )
  }

  return (
    <Fragment>
      <ImageBackground style={styles.imgBackground} resizeMode='cover' source={bgConfirmAdvanceSalary}>
        {renderHeader()}
        {profileStore.isSignedIn() ? <TouchableOpacity
          onPress={() => navigate(SCREENS.editProfile)}
          style={styles.touchUser}
          testID="profile.userInfoButton"
        >
          <LazyImage
            type='avatar'
            mode='download'
            resizeMode="cover"
            style={styles.image}
            // containerStyle={{ marginTop: 10 }}
            source={{ uri: profileStore?.avatarUser }}
          />
          <View style={styles.viewName}>
            <Text style={styles.textName}>{profileStore.fullName}</Text>
            {/*<Text style={styles.textPhone}>{profileStore.phone}</Text>*/}
            <Text style={styles.textPhone}>{profileStore.username}</Text>
          </View>
          {/* <Icon size={18} color={color.primary} style={styles.icon} name={'chevron-right'} /> */}
        </TouchableOpacity> : <View style={styles.profileBox}>
          <View style={styles.groupBtn}>
            <TouchableOpacity
              onPress={goRegister}
              style={[styles.btnRegister, { backgroundColor: '#f3f3f3' }]}
              testID="profile.registerButton"
            >
              <Text style={[styles.textBtn, { color: '#333' }]}>{t('REGISTER')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={goLogin}
              style={[styles.btnRegister, { backgroundColor: color.primary }]}
              testID="profile.loginButton"
            >
              <Text style={[styles.textBtn, { color: '#fff' }]}>{t('LOGIN')}</Text>
            </TouchableOpacity>
            {/* <TouchableOpacity
              onPress={goFastLogin}
              style={[styles.btnRegister, { backgroundColor: color.primary }]}
              testID="profile.fastLoginButton"
            >
              <Text style={[styles.textBtn, { color: '#fff' }]}>{t('FastLogin')}</Text>
            </TouchableOpacity> */}
          </View>
        </View>
        }
        <ScrollView refreshControl={<RefreshControl refreshing={false} onRefresh={() => profileStore.getProfile()} />}>
          {/* {renderAvatar()} */}
          {renderInfo()}

        </ScrollView>
        {/* <Modalize
      HeaderComponent={renderHeaderModalize}
      ref={modalizeRef}
      adjustToContentHeight
      keyboardAvoidingBehavior={'padding'}
    >
      <EditPassword closeModal={onCloseModel}/>
    </Modalize> */}

      </ImageBackground>
      <ConfirmDialog confirmText={t('DONGY')} cancelText={t('CANCEL')} onClosed={() => setIsShowConfirmLogOut(false)} isVisible={isShowConfirmLogOut} message={'Bạn có chắc chắn muốn đăng xuất khỏi ứng dụng ?'} title={'Nhắc nhở'}
        onConfirm={() => {
          setIsShowConfirmLogOut(false)
          setTimeout(() => { signOut(true) }, 100)
        }
        } />
    </Fragment>
  )
})

interface RendeMenuItemProps {
  icon: string;
  text: string;
  screen?: string;
  onPress?: () => void;
  requireLogin?: boolean;
}

const RendeMenuItem = (props: RendeMenuItemProps) => {
  const { navigate } = useNavigation<any>()
  const { profileStore } = useStores()

  const navScreen = () => {
    if (props.requireLogin === false || profileStore.isSignedIn()) {
      if (props.screen) {
        navigate(props.screen, { title: props.text })
      }
    } else {
      navigate(SCREENS.authStack, { screen: SCREENS.login })
    }
  }

  return (<TouchableOpacity
    onPress={() => props.onPress ? props.onPress() : navScreen()}
    style={{ ...styles.boxButton }}
    testID={`profile.menuItem.${props.text.toLowerCase().replace(/\s+/g, '_')}`}
  >
    <Icon name={props.icon} size={22} color={color.palette.black} style={styles.iconLeft} />
    <Text style={styles.textButton}>{props.text}</Text>
    <Icon size={18} color={'#D42023'} style={styles.iconRight} name={'chevron-right'} />
  </TouchableOpacity>)
}

RendeMenuItem.defaultProps = {
  requireLogin: true
}
