package org.pronexus.portal.app.dtos.employee;

import lombok.Data;
import jakarta.validation.constraints.NotEmpty;

/**
 * DTO for resetting passcode using OTP verification
 */
@Data
public class ResetPasscodeWithOtpCommandDto {
    
    @NotEmpty(message = "OTP is required")
    private String otp;
    
    @NotEmpty(message = "New passcode is required")
    private String newPasscode;
    
    @NotEmpty(message = "Confirm passcode is required")
    private String confirmPasscode;
}
