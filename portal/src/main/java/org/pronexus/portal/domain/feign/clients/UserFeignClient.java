package org.pronexus.portal.domain.feign.clients;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.rest.Response;
import org.keycloak.representations.idm.UserRepresentation;

import org.pronexus.portal.domain.model.RegisterUserCommandDto;
import org.pronexus.portal.domain.model.UpdateUserKeycloakCommandDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@FeignClient(value = "user-client", url = "${user.service.url}")
public interface UserFeignClient {
        @PostMapping("/api/v1/user/register/partner")
        ResponseEntity<Response<CommandResponse<UserRepresentation>>> registerPartnerUser(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @RequestBody RegisterUserCommandDto command);

        @GetMapping("/api/v1/user/check-exist")
        ResponseEntity<Response<Boolean>> checkUsernameExist(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @RequestParam("username") String username);

        @PutMapping("/api/v1/user/sync-keycloak")
        ResponseEntity<Response<CommandResponse<Void>>> updateUserKeycloak(
                        @RequestHeader(value = HttpHeaders.AUTHORIZATION) String token,
                        @RequestBody UpdateUserKeycloakCommandDto command);
}
