package org.pronexus.portal.domain.feign.adapter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.salaryadvance.commonlibrary.exception.base.ExternalServiceException;
import com.salaryadvance.commonlibrary.rest.CommandResponse;
import com.salaryadvance.commonlibrary.rest.Response;
import com.salaryadvance.commonlibrary.utils.JsonUtils;
import com.salaryadvance.commonlibrary.utils.TokenUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.representations.idm.UserRepresentation;
import org.pronexus.portal.domain.feign.clients.UserFeignClient;
import org.pronexus.portal.domain.model.RegisterUserCommandDto;
import org.pronexus.portal.domain.model.UpdateUserKeycloakCommandDto;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserClient {

    private final UserFeignClient userFeignClient;

    public UserRepresentation registerPartnerUser(RegisterUserCommandDto command) {
        try {
            String token = "Bearer " + TokenUtils.getTokenAsString();
            Response<CommandResponse<UserRepresentation>> response = userFeignClient.registerPartnerUser(token, command)
                    .getBody();
            if (response == null) {
                log.error("register partner user failed");
                throw new ExternalServiceException("Failed to register partner admin", command.getUsername());
            }
            HttpStatus httpStatus = HttpStatus.valueOf(response.getHttpStatusCode());
            if (!httpStatus.is2xxSuccessful()) {
                log.error("register partner user failed: {}", response.getDescription());
                throw new ExternalServiceException("Failed to register partner admin", response.getDescription());
            }
            if (response.getData() == null || !response.getData().isSuccess()) {
                log.error("register partner user failed: {}", JsonUtils.toJson(response));
                throw new ExternalServiceException("Failed to register partner admin", command.getUsername());
            }
            return response.getData().getDetails();
        } catch (Exception e) {
            log.error("Failed to register partner admin, {}", e.getMessage());
            throw new ExternalServiceException("Failed to register partner admin, " + e.getMessage(), e);
        }
    }

    public boolean checkUsernameExist(String username) {
        try {
            String token = "Bearer " + TokenUtils.getTokenAsString();
            Response<Boolean> response = userFeignClient.checkUsernameExist(token, username).getBody();
            if (response == null) {
                log.error("check username exist failed");
                throw new ExternalServiceException("Failed to check username exist", username);
            }
            return Boolean.TRUE.equals(response.getData());
        } catch (Exception e) {
            log.error("Failed to check username exist, {}", e.getMessage());
            throw new ExternalServiceException("Failed to check username exist, " + e.getMessage(), e);
        }
    }

    /**
     * Synchronize user information to Keycloak
     *
     * @param command DTO containing user information to update
     * @return true if successful, false otherwise
     */
    public boolean syncUserToKeycloak(UpdateUserKeycloakCommandDto command) {
        try {
            String token = "Bearer " + TokenUtils.getTokenAsString();
            Response<CommandResponse<Void>> response = userFeignClient.updateUserKeycloak(token, command).getBody();

            if (response == null) {
                log.error("Sync user to Keycloak failed - no response");
                return false;
            }

            HttpStatus httpStatus = HttpStatus.valueOf(response.getHttpStatusCode());
            if (!httpStatus.is2xxSuccessful()) {
                log.error("Sync user {} to Keycloak failed: {}", command.getUserId(), response.getDescription());
                return false;
            }

            if (response.getData() == null || !response.getData().isSuccess()) {
                String errorMessage = response.getData() != null
                        ? response.getData().getMessage()
                        : "Unknown error";
                log.error("Sync user {} to Keycloak failed: {}", command.getUserId(), errorMessage);
                return false;
            }

            log.info("Successfully synchronized user {} to Keycloak", command.getUserId());
            return true;
        } catch (Exception e) {
            log.error("Error syncing user {} to Keycloak: {}", command.getUserId(), e.getMessage(), e);
            return false;
        }
    }
}
