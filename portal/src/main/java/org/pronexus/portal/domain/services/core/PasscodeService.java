package org.pronexus.portal.domain.services.core;

import com.salaryadvance.commonlibrary.rest.CommandResponse;
import org.pronexus.portal.app.dtos.employee.ResetPasscodeCommandDto;
import org.pronexus.portal.app.dtos.employee.ResetPasscodeWithOtpCommandDto;
import org.pronexus.portal.app.dtos.employee.SetupPasscodeCommandDto;
import org.pronexus.portal.app.dtos.employee.VerifyPasscodeCommandDto;

/**
 * Service interface for managing employee passcodes for transfer confirmation
 */
public interface PasscodeService {
    /**
     * Setup a new passcode for the current employee
     * 
     * @param command DTO containing passcode and confirmation
     * @return Command response indicating success or failure
     */
    CommandResponse<Void> setupPasscode(SetupPasscodeCommandDto command);

    /**
     * Verify a passcode for transaction confirmation
     * 
     * @param command DTO containing passcode and transaction ID
     * @return Command response indicating success or failure
     */
    CommandResponse<Void> verifyPasscode(VerifyPasscodeCommandDto command);

    /**
     * Reset an existing passcode
     * 
     * @param command DTO containing old passcode, new passcode and confirmation
     * @return Command response indicating success or failure
     */
    CommandResponse<Void> resetPasscode(ResetPasscodeCommandDto command);

    /**
     * Disable passcode for the current employee
     * 
     * @return Command response indicating success or failure
     */
    CommandResponse<Void> disablePasscode();

    /**
     * Check if passcode is enabled for the current employee
     *
     * @return Command response containing boolean status
     */
    CommandResponse<Boolean> isPasscodeEnabled();

    /**
     * Reset passcode using OTP verification
     * 
     * @param command DTO containing OTP and new passcode information
     * @return Command response indicating success or failure
     */
    CommandResponse<Void> resetPasscodeWithOtp(ResetPasscodeWithOtpCommandDto command);
}
