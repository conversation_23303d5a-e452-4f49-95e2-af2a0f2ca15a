pipeline {
    agent any

    environment {
        REGISTRY = "registry.gitlab.com/pvcb-ungluong/pvcb-ungluong-backend" // Địa chỉ registry
        DOCKER_COMPOSE_FILE = 'docker-compose.yml' // File docker-compose
        GITLAB_TOKEN = '**************************'
        PROJECT_ID = '65012568'  // ID của dự án GitLab
    }

    stages {
        stage('Detect Changes') {
            steps {
                script {
                    def services = [
                        'integration',
                        'portal',
                        'salary-advance',
                        'file',
                        'user',
//                         'job',
                    ]

                    changedServices = [
                        'integration',
                        'portal',
                        'salary-advance',
                        'user',
                        'file',
//                         'job',
//                         'filebeat',
//                         'logstash',
//                         'kibana',
//                         'elasticsearch'
                    ]

                    def changedFiles = sh(
                        script: "git diff-tree --no-commit-id --name-only -r HEAD",
                        returnStdout: true
                    ).trim().split('\n')

                    for (file in changedFiles) {
                        def firstLevel = file.split('/')[0]
                        def service = services.find { it == firstLevel }
                        if (service && !changedServices.contains(service)) {
                            changedServices << service
                        }
                    }

                    if (changedServices.isEmpty()) {
                        echo 'No changes detected in services.'
                        currentBuild.result = 'SUCCESS'
                        return
                    }

                    echo "Changed services: ${changedServices.join(', ')}"
                    updateCommitStatus("running")
                }
            }
        }

        stage('Build Maven') {
            when {
                expression {
                    return changedServices && changedServices.size() > 0
                }
            }
            steps {
                script {
                    echo "Fixing permissions for target folders..."
                    sh """
                        sudo find . -type d -name "src" -exec chown -R jenkins:jenkins {} +
                    """
                    env.JAVA_HOME = '/usr/lib/jvm/temurin-23-jdk-amd64'
                    env.PATH = "${env.JAVA_HOME}/bin:${env.PATH}"
                    // Loop through each changed service and build them individually
//                     echo "Building common-lib"
//                     sh """
//                         cd common-library
//                         mvn clean install -DskipTests -X -e
//                     """
//                     changedServices.each { service ->
//                         echo "Building service: ${service}"
//                         sh """
//                             cd ${service}
//                             mvn clean install -DskipTests -X -e
//                         """
//                     }
                    sh """
                        mvn clean install -DskipTests -e
                    """
                    updateCommitStatus("running")
                }
            }
        }

        stage('Build Images') {
            when {
                expression {
                    return changedServices && changedServices.size() > 0
                }
            }
            steps {
                script {
                    def ignoreServices = ['filebeat', 'logstash', 'kibana', 'elasticsearch']
                    for (service in changedServices) {
                        if (ignoreServices.contains(service)) {
                            echo "Skipping build for ignored service: ${service}"
                            continue
                        }
                        dir(service) {
                            echo "Building Docker image for service: ${service}"
                            try {
                                sh """
                                    docker build -t ${service}:latest .
                                """
                            } catch (Exception e) {
                                echo "Build failed for service: ${service}. Error: ${e.getMessage()}"
                                error("Stopping pipeline due to build failure.")
                            }
                        }
                    }
                }
            }
        }

        stage('Deploy Updated Services') {
            when {
                expression {
                    return changedServices && changedServices.size() > 0
                }
            }
            steps {
                script {
                    for (service in changedServices) {
                        echo "Deploying updated service: ${service}"
                        sh """
                            docker-compose -f ${DOCKER_COMPOSE_FILE} up -d --force-recreate --build ${service}
                        """
                    }
                }
            }
        }
    }

    post {
        success {
            script {
                // Cập nhật trạng thái "success" cho GitLab
                updateCommitStatus("success")
            }
        }

        failure {
            script {
                // Cập nhật trạng thái "failed" cho GitLab
                updateCommitStatus("failed")
            }
        }
    }
}

def updateCommitStatus(status) {
    try {
        // Lấy commit SHA hiện tại
        def commitSha = sh(script: "git rev-parse HEAD", returnStdout: true).trim()
        echo "Commit SHA: ${commitSha}"

        // Lấy URL của build Jenkins tự động
        def buildUrl = env.BUILD_URL
        def buildNumber = env.BUILD_NUMBER
        echo "Build URL: ${buildUrl}"
        echo "Build Number: ${buildNumber}"

        // Kiểm tra thông tin project và token
        echo "Project ID: ${PROJECT_ID}"
        echo "GitLab Token: ${GITLAB_TOKEN ? 'set' : 'not set'}"

        // URL API GitLab để cập nhật trạng thái commit
        def url = "https://gitlab.com/api/v4/projects/${PROJECT_ID}/statuses/${commitSha}"

        // Cấu hình message với markdown để tạo liên kết clickable
        def message = "[Build status: ${status}](${buildUrl})"
        echo "Message: ${message}"

        // Gửi yêu cầu HTTP POST với curl và kiểm tra phản hồi
        def response = sh(script: """
            curl --header "PRIVATE-TOKEN: ${GITLAB_TOKEN}" \
                 --data "state=${status}&description=${message}&target_url=${buildUrl}" \
                 "${url}"
        """, returnStdout: true).trim()

        echo "Response from GitLab: ${response}"

        // Kiểm tra nếu có lỗi trong phản hồi
        if (response.contains("error")) {
            error "Failed to update commit status. Response: ${response}"
        }

        echo "Commit status updated to: ${status} with Jenkins build URL"
    } catch (Exception e) {
        // Nếu có lỗi, in ra thông báo và tiếp tục pipeline mà không làm gián đoạn quá trình build
        echo "An error occurred while updating commit status: ${e.message}. Continuing with the build..."
    }
}
